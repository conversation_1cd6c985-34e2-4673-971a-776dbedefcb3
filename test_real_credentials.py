#!/usr/bin/env python3
"""
Test the credentials endpoint with real JWT token.
"""

import requests
import json

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************.dWSPqvUe-F4D2jl3wHLJGAF8LnFq9x3uIdhmsqZtyMs"

def test_credentials_endpoint():
    """Test the credentials endpoint with real authentication"""
    
    print("🔧 Testing Credentials Endpoint with Real JWT")
    print("=" * 60)
    
    # Setup session with real JWT token
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {JWT_TOKEN}",
        "Content-Type": "application/json"
    })
    
    print(f"Using JWT token: {JWT_TOKEN[:50]}...")
    print(f"User ID from token: c1454e90-09ac-40f2-bde2-833387d7b645")
    
    try:
        print("\n1. Testing GET /api/v1/credentials (list credentials)")
        response = session.get(f"{API_BASE_URL}/credentials")
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Response:")
            print(f"   {json.dumps(data, indent=2)}")
        else:
            print(f"   ❌ Error Response:")
            print(f"   {response.text}")
            
            # Try to parse as JSON for better formatting
            try:
                error_data = response.json()
                print(f"   Formatted Error:")
                print(f"   {json.dumps(error_data, indent=2)}")
            except:
                pass
        
        print("\n2. Testing GET /api/v1/credentials?type=credential (explicit)")
        response = session.get(f"{API_BASE_URL}/credentials?type=credential")
        
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('credentials', []))} credentials")
        else:
            print(f"   ❌ Error: {response.text}")
        
        print("\n3. Testing GET /api/v1/credentials?type=global-variable")
        response = session.get(f"{API_BASE_URL}/credentials?type=global-variable")
        
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('credentials', []))} global variables")
        else:
            print(f"   ❌ Error: {response.text}")
        
        print("\n4. Testing POST /api/v1/credentials (create credential)")
        test_credential = {
            "key_name": "test_real_credential",
            "value": "test_secret_value_123",
            "description": "Test credential with real auth"
        }
        
        response = session.post(f"{API_BASE_URL}/credentials", json=test_credential)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"   ✅ Success! Created credential:")
            print(f"   {json.dumps(data, indent=2)}")
            
            # If successful, try to delete it
            if data.get("success") and data.get("id"):
                credential_id = data["id"]
                print(f"\n5. Testing DELETE /api/v1/credentials/{credential_id}")
                delete_response = session.delete(f"{API_BASE_URL}/credentials/{credential_id}")
                print(f"   Delete Status: {delete_response.status_code}")
                if delete_response.status_code == 200:
                    print(f"   ✅ Successfully cleaned up test credential")
                else:
                    print(f"   ⚠️  Cleanup failed: {delete_response.text}")
        else:
            print(f"   ❌ Create failed: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Real Credentials API Test")
    print("=" * 40)
    
    success = test_credentials_endpoint()
    
    if success:
        print("\n🎉 Test completed!")
        print("Check the output above for any errors or issues.")
    else:
        print("\n💥 Test failed with exception!")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
