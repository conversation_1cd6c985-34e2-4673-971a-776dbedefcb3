'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit, Plus, Eye, EyeOff } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Variable, VariableType, VariableCreate, VariableUpdate, variableService } from '@/services/variableService';

interface VariableManagerProps {
  type: VariableType;
}

export function VariableManager({ type }: VariableManagerProps) {
  const [variables, setVariables] = useState<Variable[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingVariable, setEditingVariable] = useState<Variable | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const isCredentialMode = type === VariableType.CREDENTIAL;
  const title = isCredentialMode ? 'Credentials' : 'Global Variables';
  const description = isCredentialMode 
    ? 'Manage your secure credentials and API keys'
    : 'Manage your global variables for workflows';

  useEffect(() => {
    loadVariables();
  }, [type]);

  const loadVariables = async () => {
    try {
      setLoading(true);
      const data = await variableService.listVariables(type);
      setVariables(data);
    } catch (error) {
      console.error('Failed to load variables:', error);
      toast.error('Failed to load variables');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (data: VariableCreate) => {
    try {
      await variableService.createVariable(data, type);
      toast.success(`${isCredentialMode ? 'Credential' : 'Variable'} created successfully`);
      setCreateDialogOpen(false);
      loadVariables();
    } catch (error) {
      console.error('Failed to create variable:', error);
      toast.error('Failed to create variable');
    }
  };

  const handleEdit = async (data: VariableUpdate) => {
    if (!editingVariable) return;

    try {
      await variableService.updateVariable(editingVariable.id, data, type);
      toast.success(`${isCredentialMode ? 'Credential' : 'Variable'} updated successfully`);
      setEditDialogOpen(false);
      setEditingVariable(null);
      loadVariables();
    } catch (error) {
      console.error('Failed to update variable:', error);
      toast.error('Failed to update variable');
    }
  };

  const handleDelete = async (variable: Variable) => {
    if (!confirm(`Are you sure you want to delete "${variable.key_name}"?`)) {
      return;
    }

    try {
      await variableService.deleteVariable(variable.id, type);
      toast.success(`${isCredentialMode ? 'Credential' : 'Variable'} deleted successfully`);
      loadVariables();
    } catch (error) {
      console.error('Failed to delete variable:', error);
      toast.error('Failed to delete variable');
    }
  };

  const filteredVariables = variables.filter(variable =>
    variable.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    variable.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add {isCredentialMode ? 'Credential' : 'Variable'}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <VariableForm
              type={type}
              onSubmit={handleCreate}
              onCancel={() => setCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <Input
          placeholder={`Search ${title.toLowerCase()}...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="grid gap-4">
        {filteredVariables.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <p className="text-muted-foreground">
                No {title.toLowerCase()} found.
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setCreateDialogOpen(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add your first {isCredentialMode ? 'credential' : 'variable'}
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredVariables.map((variable) => (
            <VariableCard
              key={variable.id}
              variable={variable}
              isCredentialMode={isCredentialMode}
              onEdit={(variable) => {
                setEditingVariable(variable);
                setEditDialogOpen(true);
              }}
              onDelete={handleDelete}
            />
          ))
        )}
      </div>

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          {editingVariable && (
            <VariableForm
              type={type}
              variable={editingVariable}
              onSubmit={handleEdit}
              onCancel={() => {
                setEditDialogOpen(false);
                setEditingVariable(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface VariableCardProps {
  variable: Variable;
  isCredentialMode: boolean;
  onEdit: (variable: Variable) => void;
  onDelete: (variable: Variable) => void;
}

function VariableCard({ variable, isCredentialMode, onEdit, onDelete }: VariableCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle className="text-base">{variable.key_name}</CardTitle>
          {variable.description && (
            <CardDescription>{variable.description}</CardDescription>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isCredentialMode ? "destructive" : "default"}>
            {isCredentialMode ? 'Credential' : 'Global Variable'}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(variable)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(variable)}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Value:</Label>
            <div className="text-sm">
              {isCredentialMode ? (
                variable.has_value ? (
                  <span className="text-muted-foreground">••••••••</span>
                ) : (
                  <span className="text-muted-foreground">No value</span>
                )
              ) : (
                <span className="font-mono bg-muted px-2 py-1 rounded">
                  {variable.value || 'No value'}
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Created: {new Date(variable.created_at).toLocaleDateString()}</span>
            <span>Updated: {new Date(variable.updated_at).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface VariableFormProps {
  type: VariableType;
  variable?: Variable;
  onSubmit: (data: VariableCreate | VariableUpdate) => void;
  onCancel: () => void;
}

function VariableForm({ type, variable, onSubmit, onCancel }: VariableFormProps) {
  const [formData, setFormData] = useState({
    key_name: variable?.key_name || '',
    value: '',
    description: variable?.description || ''
  });
  const [showValue, setShowValue] = useState(false);

  const isCredentialMode = type === VariableType.CREDENTIAL;
  const isEditing = !!variable;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.key_name.trim()) {
      toast.error('Name is required');
      return;
    }

    if (!formData.value.trim() && !isEditing) {
      toast.error('Value is required');
      return;
    }

    const submitData: VariableCreate | VariableUpdate = {
      key_name: formData.key_name.trim(),
      description: formData.description.trim() || undefined
    };

    if (formData.value.trim()) {
      submitData.value = formData.value;
    }

    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <DialogHeader>
        <DialogTitle>
          {isEditing ? 'Edit' : 'Create'} {isCredentialMode ? 'Credential' : 'Global Variable'}
        </DialogTitle>
        <DialogDescription>
          {isCredentialMode
            ? 'Store secure credentials and API keys'
            : 'Create variables that can be used across workflows'
          }
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label htmlFor="key_name">Name</Label>
          <Input
            id="key_name"
            value={formData.key_name}
            onChange={(e) => setFormData({ ...formData, key_name: e.target.value })}
            placeholder="Enter variable name"
            required
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="value">
              {isEditing ? 'New Value (leave empty to keep current)' : 'Value'}
            </Label>
            {isCredentialMode && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowValue(!showValue)}
              >
                {showValue ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            )}
          </div>
          {isCredentialMode ? (
            <Input
              id="value"
              type={showValue ? 'text' : 'password'}
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              placeholder={isEditing ? 'Enter new value to replace current' : 'Enter credential value'}
              required={!isEditing}
            />
          ) : (
            <Textarea
              id="value"
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              placeholder={isEditing ? 'Enter new value to replace current' : 'Enter variable value'}
              required={!isEditing}
              rows={3}
            />
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description (optional)</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Enter description"
            rows={2}
          />
        </div>
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEditing ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </form>
  );
}
