import { api } from '@/lib/api';

export enum VariableType {
  CREDENTIAL = 'credential',
  GLOBAL_VARIABLE = 'global-variable'
}

export interface Variable {
  id: string;
  key_name: string;
  description?: string;
  type: VariableType;
  value?: string;  // null for credentials, actual value for global variables
  has_value: boolean;
  created_at: string;
  updated_at: string;
  last_used_at: string;
}

export interface VariableCreate {
  key_name: string;
  value: string;
  description?: string;
}

export interface VariableUpdate {
  key_name?: string;
  value?: string;
  description?: string;
}

export interface VariableResponse {
  success: boolean;
  message: string;
  id?: string;
  key_name?: string;
}

export interface VariableListResponse {
  success: boolean;
  message: string;
  variables: Variable[];
}

export interface VariableDetailsResponse {
  success: boolean;
  message: string;
  variable?: Variable;
}

export interface VariableDeleteResponse {
  success: boolean;
  message: string;
}

export class VariableService {
  private baseUrl = '/api/v1/variables';

  /**
   * Create a new variable
   */
  async createVariable(variable: VariableCreate, type: VariableType): Promise<Variable> {
    try {
      const response = await api.post<VariableResponse>(
        `${this.baseUrl}?type=${type}`,
        variable
      );

      if (!response.data.success || !response.data.id) {
        throw new Error(response.data.message || 'Failed to create variable');
      }

      // Return the created variable (we need to fetch it to get full details)
      return await this.getVariable(response.data.id, type);
    } catch (error) {
      console.error('Error creating variable:', error);
      throw error;
    }
  }

  /**
   * List variables by type
   */
  async listVariables(type: VariableType): Promise<Variable[]> {
    try {
      const response = await api.get<VariableListResponse>(
        `${this.baseUrl}?type=${type}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to list variables');
      }

      return response.data.variables;
    } catch (error) {
      console.error('Error listing variables:', error);
      throw error;
    }
  }

  /**
   * Get a specific variable by ID and type
   */
  async getVariable(id: string, type: VariableType): Promise<Variable> {
    try {
      const response = await api.get<VariableDetailsResponse>(
        `${this.baseUrl}/${id}?type=${type}`
      );

      if (!response.data.success || !response.data.variable) {
        throw new Error(response.data.message || 'Variable not found');
      }

      return response.data.variable;
    } catch (error) {
      console.error('Error getting variable:', error);
      throw error;
    }
  }

  /**
   * Update a variable
   */
  async updateVariable(id: string, variable: VariableUpdate, type: VariableType): Promise<Variable> {
    try {
      const response = await api.put<VariableResponse>(
        `${this.baseUrl}/${id}?type=${type}`,
        variable
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to update variable');
      }

      // Return the updated variable
      return await this.getVariable(id, type);
    } catch (error) {
      console.error('Error updating variable:', error);
      throw error;
    }
  }

  /**
   * Delete a variable
   */
  async deleteVariable(id: string, type: VariableType): Promise<void> {
    try {
      const response = await api.delete<VariableDeleteResponse>(
        `${this.baseUrl}/${id}?type=${type}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to delete variable');
      }
    } catch (error) {
      console.error('Error deleting variable:', error);
      throw error;
    }
  }

  /**
   * List credentials (convenience method)
   */
  async listCredentials(): Promise<Variable[]> {
    return this.listVariables(VariableType.CREDENTIAL);
  }

  /**
   * List global variables (convenience method)
   */
  async listGlobalVariables(): Promise<Variable[]> {
    return this.listVariables(VariableType.GLOBAL_VARIABLE);
  }

  /**
   * Create a credential (convenience method)
   */
  async createCredential(credential: VariableCreate): Promise<Variable> {
    return this.createVariable(credential, VariableType.CREDENTIAL);
  }

  /**
   * Create a global variable (convenience method)
   */
  async createGlobalVariable(variable: VariableCreate): Promise<Variable> {
    return this.createVariable(variable, VariableType.GLOBAL_VARIABLE);
  }

  /**
   * Update a credential (convenience method)
   */
  async updateCredential(id: string, credential: VariableUpdate): Promise<Variable> {
    return this.updateVariable(id, credential, VariableType.CREDENTIAL);
  }

  /**
   * Update a global variable (convenience method)
   */
  async updateGlobalVariable(id: string, variable: VariableUpdate): Promise<Variable> {
    return this.updateVariable(id, variable, VariableType.GLOBAL_VARIABLE);
  }

  /**
   * Delete a credential (convenience method)
   */
  async deleteCredential(id: string): Promise<void> {
    return this.deleteVariable(id, VariableType.CREDENTIAL);
  }

  /**
   * Delete a global variable (convenience method)
   */
  async deleteGlobalVariable(id: string): Promise<void> {
    return this.deleteVariable(id, VariableType.GLOBAL_VARIABLE);
  }
}

// Export a singleton instance
export const variableService = new VariableService();
