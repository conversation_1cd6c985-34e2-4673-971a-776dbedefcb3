"use client";

import React from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Plus, Key, Eye, EyeOff, MoreVertical, Edit, Trash2 } from "lucide-react";
import { variableService, Variable, VariableType, VariableCreate, VariableUpdate } from '@/services/variableService';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import AddCredentialDialog from './AddCredentialDialog';
import EditCredentialDialog from './EditCredentialDialog';
import DeleteCredentialDialog from './DeleteCredentialDialog';

export default function CredentialsPage() {
  const [credentials, setCredentials] = React.useState<Variable[]>([]);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(true);
  const [showAddDialog, setShowAddDialog] = React.useState(false);
  const [showEditDialog, setShowEditDialog] = React.useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [editingCredential, setEditingCredential] = React.useState<Variable | null>(null);
  const [deletingCredentialId, setDeletingCredentialId] = React.useState<string | null>(null);

  // Load credentials from API on component mount
  React.useEffect(() => {
    loadCredentials();
  }, []);

  const loadCredentials = async () => {
    try {
      setIsLoading(true);
      const credentialList = await variableService.listCredentials();
      setCredentials(credentialList);
    } catch (error) {
      console.error('Error loading credentials:', error);
      // You might want to show a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCredential = async (credential: { key: string; value: string; description?: string }) => {
    try {
      const credentialData: VariableCreate = {
        key_name: credential.key,
        value: credential.value,
        description: credential.description || ''
      };

      await variableService.createCredential(credentialData);
      await loadCredentials(); // Reload the list
      setShowAddDialog(false);
    } catch (error) {
      console.error('Error creating credential:', error);
      // You might want to show an error toast here
    }
  };

  const handleEditCredential = (credential: Variable) => {
    setEditingCredential(credential);
    setShowEditDialog(true);
  };

  const handleUpdateCredential = async (updatedCredential: { key: string; value: string; description?: string }) => {
    if (editingCredential) {
      try {
        const updateData: VariableUpdate = {
          key_name: updatedCredential.key,
          value: updatedCredential.value,
          description: updatedCredential.description || editingCredential.description
        };

        await variableService.updateCredential(editingCredential.id, updateData);
        await loadCredentials(); // Reload the list
        setShowEditDialog(false);
        setEditingCredential(null);
      } catch (error) {
        console.error('Error updating credential:', error);
        // You might want to show an error toast here
      }
    }
  };

  const handleDeleteCredential = (credentialId: string) => {
    setDeletingCredentialId(credentialId);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (deletingCredentialId) {
      try {
        await variableService.deleteCredential(deletingCredentialId);
        await loadCredentials(); // Reload the list
        setShowDeleteDialog(false);
        setDeletingCredentialId(null);
      } catch (error) {
        console.error('Error deleting credential:', error);
        // You might want to show an error toast here
      }
    }
  };

  const filteredCredentials = (credentials || []).filter(cred =>
    cred.key_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (cred.description && cred.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <MainLayout>
      <div className="bg-background min-h-screen">
        {/* Main content */}
        <div className="container px-4 sm:px-8 lg:px-[100px] py-6 sm:py-8" style={{ maxWidth: "100%" }}>
          <div className="space-y-6">
            {/* Search and Controls */}
            <Card>
              <CardHeader>
                <CardTitle>Manage Credentials</CardTitle>
                <CardDescription>
                  Store and manage your API keys, database connections, and other sensitive credentials
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between gap-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search credentials..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={() => setShowAddDialog(true)}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add Credential
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Credentials List */}
            {filteredCredentials.length > 0 ? (
              <div className="grid gap-4">
                {filteredCredentials.map((credential) => (
                  <Card key={credential.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <Key className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{credential.key_name}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {credential.description || 'No description'}
                            </p>
                            <div className="flex items-center gap-4 mt-2">
                              <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                Credential
                              </span>
                              {credential.last_used_at && (
                                <span className="text-xs text-gray-500">
                                  Last used: {new Date(credential.last_used_at).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Secure
                          </span>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => handleEditCredential(credential)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteCredential(credential.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-1 flex-col items-center justify-center w-full min-h-[60vh]">
                {/* Custom Icon */}
                <div className="relative mb-8">
                  <div className="w-32 h-32 flex items-center justify-center opacity-60">
                    <img
                      src="/credential-empty.svg"
                      alt="Credential Icon"
                      className="w-full h-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    {/* Fallback Icon */}
                    <div className="w-32 h-32 flex items-center justify-center hidden">
                      <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Title */}
                <h2 className="font-[Satoshi] text-gray-900 dark:text-white text-2xl font-bold mb-2">Add your first credential</h2>
                
                {/* Description */}
                <p className="font-[Satoshi] text-gray-600 dark:text-[#A1A1AA] text-base mb-8 text-center max-w-md">
                  Store and manage your API keys, database connections, and other sensitive credentials securely for your workflows.
                </p>
                
                {/* Add Credential Button */}
                <button
                  onClick={() => setShowAddDialog(true)}
                  className="bg-[#AE00D0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition"
                >
                  <span className="text-xl font-bold">+</span> Add credential
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Dialogs */}
      <AddCredentialDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onSave={handleAddCredential}
      />

      <EditCredentialDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        credential={editingCredential}
        onSave={handleUpdateCredential}
      />

      <DeleteCredentialDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        credentialName={credentials.find(c => c.id === deletingCredentialId)?.key_name || ''}
        onConfirm={confirmDelete}
      />
    </MainLayout>
  );
}
