'use client';
import React from 'react';
import { cn } from '@/lib/utils';
import { useRouter, usePathname } from 'next/navigation';

const navItems = [
  { key: 'dashboard', label: 'Dashboard', icon: '/layout-dashboard.png', route: '/settings' },
  { key: 'credentials', label: 'Credentials', icon: '/key.png', route: '/settings/credentials' },
  { key: 'global', label: 'Global variable', icon: '/globe.png', route: '/settings/global-variable' },
  { key: 'project', label: 'Project', icon: '/network.png', route: '/settings/project' },
  { key: 'triggers', label: 'Triggers and scheduler', icon: '/bot.png', route: '/settings/triggers' },
  { key: 'marketplace', label: 'Explore marketplace', icon: '/store.png', route: '/settings/marketplace' },
];

export default function SettingsSidebar() {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div
      className={cn(
        'w-[320px] h-screen max-h-[1024px] flex flex-col bg-[#000] dark:bg-[#000] overflow-y-auto border-r border-t border-[#393939]'
      )}
      style={{ minHeight: '100vh', maxHeight: 1024 }}
    >
      {/* Upper header */}
      <div className="flex items-center justify-between px-6 pt-6 pb-4 h-[80px] border-b border-[#232323]">
        <div className="flex items-center">
          <img src="/Ruh-workflow-logo.png" alt="RUH Logo" className="h-10 object-contain" style={{ maxWidth: '120px' }} />
        </div>
        <img src="/panel-left-close.png" alt="Menu Icon" className="w-7 h-7" />
      </div>
      {/* Navigation items */}
      <div className="flex flex-col gap-2 pt-8 pl-5 pr-3 flex-1">
        {navItems.map((item) => {
          // If item has a route, check if pathname starts with it (for nested routes)
          const isSelected = item.route
            ? pathname === item.route || (item.route !== '/settings' && pathname.startsWith(item.route))
            : pathname === '/settings' && item.key === 'dashboard';
          return (
            <div
              key={item.key}
              className={cn(
                'flex items-center gap-4 px-5 py-3 rounded-2xl transition cursor-pointer',
                isSelected ? 'bg-[#232323]' : '',
                'hover:bg-[#181818]'
              )}
              style={isSelected ? { boxShadow: '0px 2px 12px 0px #0303030F' } : {}}
              onClick={() => {
                if (item.route) {
                  router.push(item.route);
                }
              }}
            >
              <img src={item.icon} alt="" className="w-6 h-6" />
              <span className={cn(
                'font-[Satoshi] text-white text-[16px] leading-[22px] text-left',
                isSelected ? 'font-bold' : 'font-normal'
              )}>
                {item.label}
              </span>
              <span className="ml-auto flex items-center">
                <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 4l5 5-5 5" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </span>
            </div>
          );
        })}
      </div>
      {/* My Account section at bottom */}
      <div className="w-full bg-[#232323] flex items-center px-4 py-3 gap-3" style={{height: '56px'}}>
        <img src="/avatar.png" alt="Avatar" className="w-7 h-7 rounded-full" />
        <span className="font-[Satoshi] text-white text-[15px]">My Account</span>
        <span className="ml-auto flex items-center">
          <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 4l5 5-5 5" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </span>
      </div>
    </div>
  );
} 