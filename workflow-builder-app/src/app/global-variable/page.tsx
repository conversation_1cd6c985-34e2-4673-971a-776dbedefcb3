'use client';
import React from 'react';
import { MainLayout } from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Plus, MoreVertical, Edit, Trash2 } from "lucide-react";
import AddVariableDialog from './AddVariableDialog';
import EditVariableDialog from './EditVariableDialog';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { variableService, Variable, VariableType, VariableCreate, VariableUpdate } from '@/services/variableService';

export default function GlobalVariablePage() {
  const [showAddDialog, setShowAddDialog] = React.useState(false);
  const [showEditDialog, setShowEditDialog] = React.useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [editingVariable, setEditingVariable] = React.useState<Variable | null>(null);
  const [deletingVariableId, setDeletingVariableId] = React.useState<string | null>(null);
  const [variables, setVariables] = React.useState<Variable[]>([]);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(true);

  // Load global variables from API on component mount
  React.useEffect(() => {
    loadGlobalVariables();
  }, []);

  const loadGlobalVariables = async () => {
    try {
      setIsLoading(true);
      const globalVariables = await variableService.listGlobalVariables();
      setVariables(globalVariables);
    } catch (error) {
      console.error('Error loading global variables:', error);
      // You might want to show a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  // Listen for custom event to open global variable dialog
  React.useEffect(() => {
    const handleOpenDialog = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('openGlobalVariableDialog', handleOpenDialog);

    return () => {
      window.removeEventListener('openGlobalVariableDialog', handleOpenDialog);
    };
  }, []);

  // Set loading to false immediately since we don't have real data loading
  React.useEffect(() => {
    setIsLoading(false);
  }, []);

  const handleAddVariable = async (variable: { key: string; value: string; secure: boolean }) => {
    try {
      const variableData: VariableCreate = {
        key_name: variable.key,
        value: variable.value,
        description: '' // You might want to add description field to the dialog
      };

      await variableService.createGlobalVariable(variableData);
      await loadGlobalVariables(); // Reload the list
      setShowAddDialog(false);
    } catch (error) {
      console.error('Error creating global variable:', error);
      // You might want to show an error toast here
    }
  };

  const handleEditVariable = (variable: Variable) => {
    setEditingVariable(variable);
    setShowEditDialog(true);
  };

  const handleUpdateVariable = async (updatedVariable: { key: string; value: string; secure: boolean }) => {
    if (editingVariable) {
      try {
        const updateData: VariableUpdate = {
          key_name: updatedVariable.key,
          value: updatedVariable.value,
          description: editingVariable.description
        };

        await variableService.updateGlobalVariable(editingVariable.id, updateData);
        await loadGlobalVariables(); // Reload the list
        setShowEditDialog(false);
        setEditingVariable(null);
      } catch (error) {
        console.error('Error updating global variable:', error);
        // You might want to show an error toast here
      }
    }
  };

  const handleDeleteVariable = (variableId: string) => {
    setDeletingVariableId(variableId);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (deletingVariableId) {
      try {
        await variableService.deleteGlobalVariable(deletingVariableId);
        await loadGlobalVariables(); // Reload the list
        setShowDeleteDialog(false);
        setDeletingVariableId(null);
      } catch (error) {
        console.error('Error deleting global variable:', error);
        // You might want to show an error toast here
      }
    }
  };

  const filteredVariables = variables.filter((variable) =>
    variable.key_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (variable.value && variable.value.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <MainLayout>
            <div className="bg-background min-h-screen">
              <div className="container py-6 sm:py-8" style={{ maxWidth: "100%" }}>
      <div className="bg-background min-h-screen">
        {/* Main content */}
        <div className="px-4 md:px-6">

              {/* Search and Sort Controls - Only show when there are variables */}
        {variables.length > 0 && (
          <div className="mb-6 flex flex-col items-center justify-between gap-4 sm:flex-row sm:gap-6 max-w-full mx-auto">
            <div className="relative w-full max-w-sm mx-auto sm:mx-0 sm:max-w-xs md:max-w-sm lg:w-72">
              <Search
                className="absolute top-1/2 left-3 h-4 w-4 text-muted-foreground transform -translate-y-1/2"
                aria-hidden="true"
              />
              <Input
                placeholder="Search global variables..."
                className="pl-10 rounded-sm text-sm font-[Satoshi] w-full"
                style={{
                  height: '44px',
                  paddingTop: '0',
                  paddingBottom: '0',
                  lineHeight: '44px',
                  display: 'flex',
                  alignItems: 'center',
                  background: 'var(--Input-black, #F9FAFB)',
                  border: '1px solid var(--Border, #E5E7EB)',
                  color: 'var(--search-text, #A1A1AA)'
                }}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                aria-label="Search global variables"
                id="global-variable-search"
                name="global-variable-search"
              />
            </div>
            <div className="flex items-center gap-2 flex-shrink-0 self-center">
              <span className="text-sm text-muted-foreground font-[Satoshi] whitespace-nowrap">Sort by:</span>
              <Select defaultValue="latest">
                <SelectTrigger 
                  className="w-[120px] sm:w-[140px] h-9 rounded-sm font-[Satoshi]"
                  style={{
                    background: 'var(--Input-black, #F9FAFB)',
                    border: '1px solid var(--Border, #E5E7EB)',
                    color: 'var(--White, #000000)'
                  }}
                >
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent 
                  className="font-[Satoshi]"
                  style={{
                    background: 'var(--Input-black, #F9FAFB)',
                    border: '1px solid var(--Border, #E5E7EB)',
                    color: 'var(--White, #000000)'
                  }}
                >
                  <SelectItem value="latest" className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939]">Latest update</SelectItem>
                  <SelectItem value="oldest" className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939]">Oldest update</SelectItem>
                  <SelectItem value="name-asc" className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939]">Name A-Z</SelectItem>
                  <SelectItem value="name-desc" className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939]">Name Z-A</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

      {/* Search and Sort Skeleton Loaders */}
      {isLoading && (
        <div className="mb-6">
          <div className="flex items-center justify-between gap-4 max-w-full mx-auto">
            <div className="relative w-72">
              <Skeleton className="h-11 w-full rounded-sm" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="w-[140px] h-11 rounded-sm" />
            </div>
          </div>
        </div>
      )}
   

      {/* Variables Grid */}
      {isLoading ? (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-x-12 gap-y-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="rounded-sm min-h-[80px]" style={{ background: 'var(--container-Background, #FFFFFF)', border: '1px solid var(--Border_color, #E5E7EB)' }}>
                <CardContent className="px-2.5 py-3 flex flex-col justify-center h-full">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2.5 flex-1 min-w-0">
                      <Skeleton className="w-8 h-8 rounded-sm" />
                      <div className="flex-1 min-w-0">
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                    <Skeleton className="w-6 h-6 rounded" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : filteredVariables.length > 0 ? (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-x-12 gap-y-6">
            {filteredVariables.map((variable) => (
              <Card key={variable.id} className="hover:bg-gray-50 dark:hover:bg-[#1C1C1C] transition-all duration-200 hover:scale-[1.02] rounded-sm min-h-[80px]" style={{ background: 'var(--container-Background, #FFFFFF)', border: '1px solid var(--Border_color, #E5E7EB)' }}>
                <CardContent className="px-2.5 py-3 flex flex-col justify-center h-full">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2.5 flex-1 min-w-0">
                      <div className="w-8 h-8 bg-[#F4F4F5] dark:bg-[#2E2E2E] rounded-sm flex items-center justify-center flex-shrink-0">
                        <img
                          src="/RuhIcon.png"
                          alt="Variable Icon"
                          className="h-4.5 w-5"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                        {/* Fallback Icon */}
                        <span className="text-gray-900 dark:text-white font-bold text-sm hidden">{variable.key_name.charAt(0).toUpperCase()}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-gray-900 dark:text-white font-semibold text-sm font-[Satoshi] truncate">
                          {variable.key_name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ')}
                        </h3>
                        <p className="text-[#6F6F6F] text-xs font-[Satoshi] mt-0.5 truncate">
                          {variable.value ? variable.value.substring(0, 18) + (variable.value.length > 18 ? '...' : '') : 'No value'}
                        </p>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-white/80 hover:text-white">
                          <div className="flex flex-col items-center justify-center gap-0.5">
                            <div className="w-1 h-1 bg-current rounded-full"></div>
                            <div className="w-1 h-1 bg-current rounded-full"></div>
                            <div className="w-1 h-1 bg-current rounded-full"></div>
                          </div>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white dark:bg-[#2E2E2E] border-gray-200 dark:border-[#393939] shadow-lg">
                        <DropdownMenuItem 
                          className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939] cursor-pointer"
                          onClick={() => handleEditVariable(variable)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#393939] cursor-pointer"
                          onClick={() => handleDeleteVariable(variable.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : null}

      {/* No Search Results */}
      {variables.length > 0 && filteredVariables.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">No variables found</h3>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-6">
            No variables match your search criteria. Try adjusting your search terms.
          </p>
          <button
            onClick={() => setSearchTerm('')}
            className="bg-[#AE00D0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-6 py-2 text-sm transition"
          >
            Clear Search
          </button>
        </div>
      )}

      {/* Empty State */}
      {variables.length === 0 && (
        <div className="flex flex-1 flex-col items-center justify-center w-full min-h-[60vh]">
          {/* Custom Icon */}
          <div className="relative mb-8">
            <div className="w-32 h-32 flex items-center justify-center opacity-60">
              <Image
                src="/global-variable-empty.svg"
                alt="Global Variables Icon"
                width={128}
                height={128}
                className="w-full h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
              {/* Fallback Icon */}
              <div className="w-32 h-32 flex items-center justify-center hidden">
                <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          {/* Title */}
          <h2 className="font-[Satoshi] text-gray-900 dark:text-white text-2xl font-bold mb-2">Add a global variable</h2>
          
          {/* Description */}
          <p className="font-[Satoshi] text-gray-600 dark:text-[#A1A1AA] text-base mb-8 text-center max-w-md">
            Create reusable variables that can be shared across all your workflows. Store API keys, configuration values, and other data that your workflows need to function.
          </p>
          
          {/* Add Variables Button */}
          <button 
            onClick={() => setShowAddDialog(true)}
            className="bg-[#AE00D0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition"
          >
            <span className="text-xl font-bold">+</span> Add variables
          </button>
        </div>
      )}

      <AddVariableDialog open={showAddDialog} onOpenChange={setShowAddDialog} onSave={handleAddVariable} />
      
      {/* Edit Variable Dialog */}
      {editingVariable && (
        <EditVariableDialog 
          open={showEditDialog} 
          onOpenChange={setShowEditDialog} 
          onSave={handleUpdateVariable}
          variable={editingVariable}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={confirmDelete}
        variableName={variables.find(v => v.id === deletingVariableId)?.key_name || ''}
      />
          </div>
        </div>
        </div>
        </div>
    </MainLayout>
  );
} 