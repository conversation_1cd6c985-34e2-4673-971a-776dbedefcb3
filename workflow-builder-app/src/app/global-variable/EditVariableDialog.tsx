'use client';
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogT<PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { X } from 'lucide-react';
import { Variable } from '@/services/variableService';

interface EditVariableDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave?: (variable: { key: string; value: string; secure: boolean }) => void;
  variable: Variable | null;
}

export default function EditVariableDialog({ open, onOpenChange, onSave, variable }: EditVariableDialogProps) {
  const [key, setKey] = useState('');
  const [value, setValue] = useState('');
  const [secure, setSecure] = useState(false);

  // Update form when variable prop changes
  useEffect(() => {
    if (variable) {
      setKey(variable.key_name);
      setValue(variable.value || '');
      setSecure(false); // Global variables are not secure by default
    }
  }, [variable]);

  const handleSave = () => {
    if (onSave) {
      onSave({ key, value, secure });
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[800px] w-full rounded-[20px] bg-white dark:bg-[#232326] border border-gray-200 dark:border-none shadow-[0_8px_32px_0_rgba(0,0,0,0.40)] p-6 font-[Satoshi]">
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-white text-[22px] font-bold mb-6 leading-tight">Edit Variable</DialogTitle>
        </DialogHeader>
        <div className="mb-4">
          <label className="block text-gray-900 dark:text-white text-[16px] font-medium mb-2">Key</label>
          <input
            className="w-full h-10 rounded-lg bg-gray-50 dark:bg-[#18181B] text-gray-900 dark:text-white font-[Satoshi] px-4 text-[16px] placeholder:text-gray-500 dark:placeholder:text-[#A1A1AA] border border-gray-300 dark:border-[#393939] outline-none focus:border-[#A020F0]"
            placeholder="e.g., companyName"
            value={key}
            onChange={e => setKey(e.target.value)}
          />
        </div>
        <div className="mb-4">
          <label className="block text-gray-900 dark:text-white text-[16px] font-medium mb-2">Value</label>
          <textarea
            className="w-full h-16 rounded-lg bg-gray-50 dark:bg-[#18181B] text-gray-900 dark:text-white font-[Satoshi] px-4 py-3 text-[16px] placeholder:text-gray-500 dark:placeholder:text-[#A1A1AA] border border-gray-300 dark:border-[#393939] outline-none resize-none focus:border-[#A020F0]"
            placeholder="e.g., companyName"
            value={value}
            onChange={e => setValue(e.target.value)}
          />
        </div>
        <div className="flex items-center mb-6">
          <label className="text-gray-900 dark:text-white text-[16px] font-medium mr-4">Secure credentials</label>
          <button
            type="button"
            className={`w-10 h-6 flex items-center rounded-full p-1 transition-colors duration-200 ${secure ? 'bg-[#A020F0]' : 'bg-gray-300 dark:bg-[#393939]'}`}
            onClick={() => setSecure(s => !s)}
            aria-pressed={secure}
            style={{ minWidth: 40, minHeight: 24 }}
          >
            <span
              className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform duration-200 ${secure ? 'translate-x-4' : 'translate-x-0'}`}
              style={{ minWidth: 16, minHeight: 16 }}
            />
          </button>
        </div>
        <div className="flex justify-center gap-6 mt-4">
          <button
            type="button"
            className="bg-white text-gray-900 dark:text-black font-[Satoshi] font-medium rounded-md px-8 py-2.5 text-sm border border-gray-300 dark:border-[#393939] hover:bg-gray-50 dark:hover:bg-gray-100 transition-colors min-w-[100px]"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-[#A020F0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-md px-8 py-2.5 text-sm transition-colors min-w-[100px]"
            onClick={handleSave}
          >
            Update
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 