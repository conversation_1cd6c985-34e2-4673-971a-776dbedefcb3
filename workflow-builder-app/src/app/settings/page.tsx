'use client';
import React from 'react';
import { MainLayout } from "@/components/layout/MainLayout";

export default function SettingsPage() {
  return (
    <MainLayout>
      <div className="bg-background min-h-screen">
        {/* Main content */}
        <div className="container px-4 sm:px-8 lg:px-[100px] py-6 sm:py-8" style={{ maxWidth: "100%" }}>
          <div className="flex flex-1 flex-col items-center justify-center w-full min-h-[60vh]">
            {/* Custom Icon */}
            <div className="relative mb-8">
              <div className="w-32 h-32 flex items-center justify-center opacity-60">
                <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            {/* Title */}
            <h2 className="font-[Satoshi] text-gray-900 dark:text-white text-2xl font-bold mb-2">Settings</h2>
            
            {/* Description */}
            <p className="font-[Satoshi] text-gray-600 dark:text-gray-400 text-center max-w-md mb-8">
              This page is coming soon. We're working hard to bring you comprehensive application settings and preferences.
            </p>
            
            {/* Coming Soon Badge */}
            <div className="inline-flex items-center px-8 py-3 rounded-lg bg-brand-primary hover:bg-brand-primary/90 text-white text-base font-medium transition-colors duration-200">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Coming Soon
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 