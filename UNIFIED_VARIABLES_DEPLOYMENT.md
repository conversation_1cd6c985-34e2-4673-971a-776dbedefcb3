# Unified Variables System Deployment Guide

This document provides a comprehensive guide for deploying the unified variables system that handles both credentials and global variables.

## Overview

The unified variables system consolidates credential management and global variable management into a single, type-aware system. This provides:

- **Unified API**: Single set of endpoints for both credentials and global variables
- **Type-based Security**: Automatic encryption for credentials, plain text for global variables
- **Consistent UI**: Shared components with type-specific behavior
- **Backward Compatibility**: Existing credentials are migrated seamlessly

## Pre-Deployment Checklist

### 1. Database Preparation
- [ ] Backup existing credentials table
- [ ] Verify database connectivity
- [ ] Ensure sufficient storage for new variables table
- [ ] Test migration script in staging environment

### 2. Service Dependencies
- [ ] User service is running and accessible
- [ ] API Gateway is configured correctly
- [ ] gRPC communication is working
- [ ] Encryption service is available

### 3. Frontend Dependencies
- [ ] Workflow builder app is built and ready
- [ ] New variable service is integrated
- [ ] UI components are tested
- [ ] Navigation is updated

## Deployment Steps

### Phase 1: Database Migration

1. **Run Migration Script**
   ```bash
   cd user-service
   python migrations/migrate_credentials_to_variables.py migrate
   ```

2. **Verify Migration**
   ```bash
   python migrations/migrate_credentials_to_variables.py verify
   ```

3. **Check Data Integrity**
   - Verify all credentials are migrated
   - Check encryption is working
   - Validate foreign key relationships

### Phase 2: Backend Services

1. **Update User Service**
   - Deploy new variable models
   - Update gRPC proto definitions
   - Restart user service

2. **Update API Gateway**
   - Deploy new variable routes
   - Update service client
   - Restart API gateway

3. **Test Backend APIs**
   ```bash
   # Test credential endpoints
   curl -X GET "http://localhost:8000/api/v1/variables?type=credential" \
        -H "Authorization: Bearer YOUR_TOKEN"
   
   # Test global variable endpoints
   curl -X GET "http://localhost:8000/api/v1/variables?type=global-variable" \
        -H "Authorization: Bearer YOUR_TOKEN"
   ```

### Phase 3: Frontend Deployment

1. **Deploy Frontend Updates**
   - Build and deploy workflow builder app
   - Update navigation to include credentials page
   - Test unified variable manager

2. **Verify UI Functionality**
   - Test credential creation/editing (values hidden)
   - Test global variable creation/editing (values visible)
   - Verify type isolation
   - Check responsive design

### Phase 4: Testing and Validation

1. **Run Comprehensive Tests**
   ```bash
   python test_unified_variables.py
   ```

2. **Manual Testing**
   - Create credentials and verify encryption
   - Create global variables and verify visibility
   - Test CRUD operations for both types
   - Verify search and filtering

3. **Performance Testing**
   - Load test variable endpoints
   - Monitor database performance
   - Check memory usage

## Post-Deployment Tasks

### 1. Monitoring Setup
- [ ] Set up alerts for variable API endpoints
- [ ] Monitor database performance
- [ ] Track encryption/decryption operations
- [ ] Monitor frontend error rates

### 2. Documentation Updates
- [ ] Update API documentation
- [ ] Create user guides for new features
- [ ] Update developer documentation
- [ ] Record deployment notes

### 3. Cleanup (After Verification)
```bash
# Only after thorough testing and verification
python migrations/migrate_credentials_to_variables.py cleanup
```

## Rollback Plan

If issues are encountered, follow this rollback procedure:

### 1. Immediate Rollback
```bash
# Rollback database changes
python migrations/migrate_credentials_to_variables.py rollback

# Revert to previous service versions
# (Use your deployment system's rollback feature)
```

### 2. Data Recovery
- Restore from backup if necessary
- Verify data integrity
- Test existing functionality

### 3. Communication
- Notify stakeholders of rollback
- Document issues encountered
- Plan remediation steps

## Configuration

### Environment Variables

**User Service:**
```env
# Encryption settings
ENCRYPTION_KEY_PREFIX=user_encryption_
SECRET_MANAGER_ENABLED=true

# Database settings
DATABASE_URL=postgresql://user:pass@host:port/db
```

**API Gateway:**
```env
# Service endpoints
USER_SERVICE_URL=grpc://user-service:50051

# API settings
API_V1_STR=/api/v1
```

### Feature Flags

Consider using feature flags for gradual rollout:

```env
# Enable unified variables system
UNIFIED_VARIABLES_ENABLED=true

# Enable new UI components
NEW_VARIABLE_UI_ENABLED=true
```

## Security Considerations

### 1. Encryption
- All credential values are encrypted at rest
- Encryption keys are managed securely
- Regular key rotation is recommended

### 2. Access Control
- API endpoints require authentication
- Users can only access their own variables
- Type-based access controls are enforced

### 3. Audit Logging
- All variable operations are logged
- Include user ID, operation type, and timestamp
- Monitor for suspicious activity

## Troubleshooting

### Common Issues

**Migration Fails:**
- Check database connectivity
- Verify user permissions
- Review migration logs

**API Endpoints Not Working:**
- Verify gRPC service is running
- Check service discovery
- Review API gateway logs

**Frontend Issues:**
- Clear browser cache
- Check console for JavaScript errors
- Verify API connectivity

**Encryption Problems:**
- Verify encryption service is running
- Check encryption keys are accessible
- Review user service logs

### Support Contacts

- **Database Issues**: DBA Team
- **Backend Issues**: Backend Development Team
- **Frontend Issues**: Frontend Development Team
- **Security Issues**: Security Team

## Success Metrics

Track these metrics to measure deployment success:

- **Migration Success Rate**: 100% of credentials migrated
- **API Response Times**: < 200ms for variable operations
- **Error Rates**: < 1% for all variable endpoints
- **User Adoption**: Usage of new variable features
- **Security**: No credential value leaks in logs/responses

## Next Steps

After successful deployment:

1. **User Training**: Provide training on new features
2. **Documentation**: Create comprehensive user guides
3. **Optimization**: Monitor and optimize performance
4. **Feature Enhancement**: Plan additional variable features
5. **Integration**: Integrate with workflow execution engine

---

**Deployment Date**: _____________
**Deployed By**: _____________
**Verified By**: _____________
**Sign-off**: _____________
