#!/usr/bin/env python3
"""
Debug script to test the enhanced credentials API and see what's happening.
"""

import requests
import json

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"

def test_credentials_endpoint():
    """Test the credentials endpoint to see what's happening"""
    
    print("🔧 Testing Enhanced Credentials Endpoint")
    print("=" * 50)
    
    # Setup session
    session = requests.Session()
    session.headers.update({
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    })
    
    try:
        print("\n1. Testing GET /api/v1/credentials (backward compatibility)")
        response = session.get(f"{API_BASE_URL}/credentials")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code != 200:
            print("❌ Basic credentials endpoint is failing")
            return False
        
        print("\n2. Testing GET /api/v1/credentials?type=credential (explicit)")
        response = session.get(f"{API_BASE_URL}/credentials?type=credential")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        print("\n3. Testing GET /api/v1/credentials?type=global-variable")
        response = session.get(f"{API_BASE_URL}/credentials?type=global-variable")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        print("\n4. Testing POST /api/v1/credentials (create credential)")
        test_credential = {
            "key_name": "debug_test_credential",
            "value": "debug_secret_value",
            "description": "Debug test credential"
        }
        
        response = session.post(f"{API_BASE_URL}/credentials", json=test_credential)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            if data.get("success") and data.get("id"):
                credential_id = data["id"]
                print(f"✅ Created credential: {credential_id}")
                
                # Test delete
                print(f"\n5. Testing DELETE /api/v1/credentials/{credential_id}")
                response = session.delete(f"{API_BASE_URL}/credentials/{credential_id}")
                print(f"Status Code: {response.status_code}")
                print(f"Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception occurred: {str(e)}")
        return False

def test_health_check():
    """Test if the API Gateway is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        print(f"Health Check - Status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False

def main():
    """Main debug function"""
    print("🚀 API Debug Script")
    print("=" * 30)
    
    # Check if API Gateway is running
    if not test_health_check():
        print("❌ API Gateway is not responding. Make sure it's running on localhost:8000")
        return
    
    print("✅ API Gateway is running")
    
    # Test credentials endpoint
    test_credentials_endpoint()

if __name__ == "__main__":
    main()
