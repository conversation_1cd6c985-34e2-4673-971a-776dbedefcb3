[tool.poetry]
name = "orchestration-engine"
version = "0.1.0"
description = "Orchestrates MCP server nodes"
authors = ["PratyushNag"]
#package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
tiktoken = "^0.9.0"
aiokafka = "^0.12.0"
jsonschema = "^4.23.0"
autogen-agentchat = "^*******"
autogen-core = "^*******"
autogen-ext = "^*******"
openai = "^1.66.5"
mcp = "^1.4.1"
redis = "^5.2.1"
psycopg2-binary = "^2.9.9"
python-dotenv = "^1.1.0"
werkzeug = "^3.1.3"
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
pytest-asyncio = "^0.26.0"
fastapi = "^0.115.12"
httpx = "^0.28.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
