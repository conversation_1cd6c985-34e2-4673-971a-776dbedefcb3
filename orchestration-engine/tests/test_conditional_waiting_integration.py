"""
Integration tests for conditional waiting transition cleanup functionality.

Tests the complete flow of removing unreachable waiting transitions after
conditional routing decisions, simulating the edge case described in the issue.
"""

import pytest
import asyncio
from unittest.mock import Mock, MagicMock, patch
from app.core_.conditional_routing_handler import <PERSON>ditional<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core_.state_manager import Workflow<PERSON>tateManager
from app.core_.transition_handler import TransitionHandler


class TestConditionalWaitingIntegration:
    """Integration test suite for conditional waiting transition cleanup."""

    @pytest.fixture
    def state_manager(self):
        """Create a WorkflowStateManager instance for testing."""
        return WorkflowStateManager(workflow_id="test_workflow")

    @pytest.fixture
    def dependency_map(self):
        """
        Create a dependency map for the edge case scenario:

        1. Conditional Node has two paths: path_true and path_false
        2. Node Y depends on path_true (so it should be removed if path_false is chosen)
        3. Node Z depends on both paths (so it should be preserved regardless)
        """
        return {
            "node_x": [],
            "conditional_node": [],
            "path_true": ["conditional_node"],
            "path_false": ["conditional_node"],
            "node_y": ["path_true"],  # Only reachable through path_true
            "node_z": ["path_true", "path_false"],  # Reachable through both paths
        }

    @pytest.fixture
    def transitions_by_id(self):
        """Create transitions for the test scenario."""
        return {
            "node_x": {
                "id": "node_x",
                "sequence": 1,
                "transition_type": "initial",
                "node_info": {
                    "node_id": "node_x",
                    "tools_to_use": [{"tool_name": "test_tool"}],
                },
            },
            "conditional_node": {
                "id": "conditional_node",
                "sequence": 2,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "conditional_node",
                    "tools_to_use": [
                        {
                            "tool_name": "conditional",
                            "tool_parameters": {
                                "conditions": [
                                    {
                                        "condition": "input.value > 10",
                                        "next_transition": "path_true",
                                    },
                                    {
                                        "condition": "input.value <= 10",
                                        "next_transition": "path_false",
                                    },
                                ]
                            },
                        }
                    ],
                },
            },
            "node_y": {
                "id": "node_y",
                "sequence": 3,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "node_y",
                    "tools_to_use": [{"tool_name": "test_tool"}],
                },
            },
            "path_true": {
                "id": "path_true",
                "sequence": 4,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "path_true",
                    "tools_to_use": [{"tool_name": "test_tool"}],
                },
            },
            "path_false": {
                "id": "path_false",
                "sequence": 5,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "path_false",
                    "tools_to_use": [{"tool_name": "test_tool"}],
                },
            },
        }

    @pytest.fixture
    def transition_handler(self, state_manager, dependency_map, transitions_by_id):
        """Create a TransitionHandler instance for testing."""
        mock_tool_executor = Mock()
        mock_workflow_utils = Mock()
        mock_result_callback = Mock()

        handler = TransitionHandler(
            state_manager=state_manager,
            transitions_by_id=transitions_by_id,
            nodes={},  # Not needed for this test
            dependency_map=dependency_map,
            workflow_utils=mock_workflow_utils,
            tool_executor=mock_tool_executor,
            result_callback=mock_result_callback,
        )

        return handler

    @pytest.mark.asyncio
    async def test_edge_case_scenario(self, state_manager, dependency_map):
        """
        Test the edge case scenario where:
        1. Node X completes
        2. Node Y is waiting (depends on X and Conditional)
        3. Conditional Node returns false (path_false)
        4. Node Y should be removed from waiting because it's only reachable through path_true
        """
        # Setup initial state
        state_manager.waiting_transitions.add("node_y")  # Depends on path_true
        state_manager.waiting_transitions.add("node_z")  # Depends on both paths
        state_manager.waiting_transitions.add("path_true")  # Also waiting

        # Create conditional transition
        conditional_transition = {
            "id": "conditional_node",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "tool_parameters": {
                            "conditions": [
                                {
                                    "condition": "input.value > 10",
                                    "next_transition": "path_true",
                                },
                                {
                                    "condition": "input.value <= 10",
                                    "next_transition": "path_false",
                                },
                            ]
                        },
                    }
                ]
            },
        }

        # Routing decision: chose path_false (path_true is unchosen)
        routing_decision = {
            "target_transition": "path_false",
            "matched_condition": 2,
            "condition_result": True,
        }

        # Create handler and call the removal method
        handler = ConditionalRoutingHandler()
        removed = handler._remove_unreachable_waiting_transitions(
            conditional_transition, routing_decision, state_manager, dependency_map
        )

        # Verify that path_true was removed (unreachable)
        assert "path_true" in removed
        assert "path_true" not in state_manager.waiting_transitions

        # Verify that node_y was removed (only reachable through unchosen path_true)
        assert "node_y" in removed
        assert "node_y" not in state_manager.waiting_transitions

        # Verify that node_z was NOT removed (reachable through chosen path_false)
        assert "node_z" not in removed
        assert "node_z" in state_manager.waiting_transitions

    @pytest.mark.asyncio
    async def test_multiple_path_preservation(self, state_manager):
        """
        Test that transitions with multiple paths are preserved.
        """
        # Create a scenario where a transition has multiple dependency paths
        # In this case, multi_path_node depends on BOTH conditional paths
        dependency_map = {
            "conditional_node": [],
            "path_true": ["conditional_node"],
            "path_false": ["conditional_node"],
            "multi_path_node": [
                "path_true",
                "path_false",
            ],  # Depends on BOTH paths - should be preserved regardless of choice
        }

        transitions_by_id = {
            "conditional_node": {
                "id": "conditional_node",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_name": "conditional",
                            "tool_parameters": {
                                "conditions": [
                                    {
                                        "condition": "input.value > 10",
                                        "next_transition": "path_true",
                                    },
                                    {
                                        "condition": "input.value <= 10",
                                        "next_transition": "path_false",
                                    },
                                ]
                            },
                        }
                    ]
                },
            }
        }

        # Setup state: multi_path_node is waiting
        state_manager.waiting_transitions.add("multi_path_node")

        # Create handler
        handler = ConditionalRoutingHandler()

        # Routing decision: chose path_false (path_true is unchosen)
        routing_decision = {
            "target_transition": "path_false",
            "matched_condition": 2,
            "condition_result": True,
        }

        # Call the removal method
        removed = handler._remove_unreachable_waiting_transitions(
            transitions_by_id["conditional_node"],
            routing_decision,
            state_manager,
            dependency_map,
        )

        # multi_path_node should NOT be removed because it depends on BOTH paths
        # Since path_false was chosen, multi_path_node is still reachable
        assert "multi_path_node" not in removed
        assert "multi_path_node" in state_manager.waiting_transitions

    @pytest.mark.asyncio
    async def test_no_false_positives(self, state_manager):
        """
        Test that unrelated waiting transitions are not affected.
        """
        dependency_map = {
            "conditional_node": [],
            "path_true": ["conditional_node"],
            "path_false": ["conditional_node"],
            "unrelated_node": ["some_other_node"],
            "some_other_node": [],
        }

        transitions_by_id = {
            "conditional_node": {
                "id": "conditional_node",
                "node_info": {
                    "tools_to_use": [
                        {
                            "tool_name": "conditional",
                            "tool_parameters": {
                                "conditions": [
                                    {
                                        "condition": "input.value > 10",
                                        "next_transition": "path_true",
                                    },
                                    {
                                        "condition": "input.value <= 10",
                                        "next_transition": "path_false",
                                    },
                                ]
                            },
                        }
                    ]
                },
            }
        }

        # Setup state: unrelated_node is waiting
        state_manager.waiting_transitions.add("unrelated_node")

        # Create handler
        handler = ConditionalRoutingHandler()

        # Routing decision
        routing_decision = {
            "target_transition": "path_true",
            "matched_condition": 1,
            "condition_result": True,
        }

        # Call the removal method
        removed = handler._remove_unreachable_waiting_transitions(
            transitions_by_id["conditional_node"],
            routing_decision,
            state_manager,
            dependency_map,
        )

        # unrelated_node should NOT be affected
        assert "unrelated_node" not in removed
        assert "unrelated_node" in state_manager.waiting_transitions
