"""
Unit tests for conditional waiting transition cleanup functionality.

Tests the new helper methods in ConditionalRoutingHandler that handle
removal of unreachable waiting transitions after conditional routing decisions.
"""

import pytest
from unittest.mock import Mock, MagicMock
from app.core_.conditional_routing_handler import ConditionalRoutingHandler


class TestConditionalWaitingCleanup:
    """Test suite for conditional waiting transition cleanup functionality."""

    @pytest.fixture
    def handler(self):
        """Create a ConditionalRoutingHandler instance for testing."""
        mock_logger = Mock()
        return ConditionalRoutingHandler(logger=mock_logger)

    @pytest.fixture
    def sample_conditional_transition(self):
        """Sample conditional transition configuration."""
        return {
            "id": "conditional_node",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "tool_parameters": {
                            "conditions": [
                                {
                                    "condition": "input.value > 10",
                                    "next_transition": "path_a",
                                },
                                {
                                    "condition": "input.value <= 10",
                                    "next_transition": "path_b",
                                },
                            ],
                            "default_transition": "default_path",
                        },
                    }
                ]
            },
        }

    @pytest.fixture
    def sample_dependency_map(self):
        """Sample dependency map for testing."""
        return {
            "conditional_node": [],
            "path_a": ["conditional_node"],
            "path_b": ["conditional_node"],
            "default_path": ["conditional_node"],
            "node_y": ["conditional_node", "node_x"],
            "node_x": [],
            "final_node": ["path_a", "path_b"],
        }

    def test_extract_all_possible_transitions_basic(
        self, handler, sample_conditional_transition
    ):
        """Test extracting all possible transitions from conditional config."""
        result = handler._extract_all_possible_transitions(
            sample_conditional_transition
        )

        expected = ["path_a", "path_b", "default_path"]
        assert sorted(result) == sorted(expected)

    def test_extract_all_possible_transitions_no_default(self, handler):
        """Test extracting transitions when no default is specified."""
        transition = {
            "id": "conditional_node",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "tool_parameters": {
                            "conditions": [
                                {
                                    "condition": "input.value > 10",
                                    "next_transition": "path_a",
                                },
                                {
                                    "condition": "input.value <= 10",
                                    "next_transition": "path_b",
                                },
                            ]
                        },
                    }
                ]
            },
        }

        result = handler._extract_all_possible_transitions(transition)
        expected = ["path_a", "path_b"]
        assert sorted(result) == sorted(expected)

    def test_extract_all_possible_transitions_no_conditional_tool(self, handler):
        """Test extracting transitions when no conditional tool is found."""
        transition = {
            "id": "regular_node",
            "node_info": {
                "tools_to_use": [{"tool_name": "regular_tool", "tool_parameters": {}}]
            },
        }

        result = handler._extract_all_possible_transitions(transition)
        assert result == []

    def test_build_forward_dependency_graph_basic(self, handler, sample_dependency_map):
        """Test building forward dependency graph."""
        result = handler._build_forward_dependency_graph(sample_dependency_map)

        # Check that conditional_node has the right dependents
        assert sorted(result["conditional_node"]) == sorted(
            ["path_a", "path_b", "default_path", "node_y"]
        )

        # Check that node_x has node_y as dependent
        assert result["node_x"] == ["node_y"]

        # Check that path_a and path_b have final_node as dependent
        assert result["path_a"] == ["final_node"]
        assert result["path_b"] == ["final_node"]

    def test_build_forward_dependency_graph_empty(self, handler):
        """Test building forward dependency graph with empty input."""
        result = handler._build_forward_dependency_graph({})
        assert result == {}

    def test_find_reachable_transitions_basic(self, handler):
        """Test finding reachable transitions using BFS."""
        forward_graph = {
            "start": ["middle1", "middle2"],
            "middle1": ["end1"],
            "middle2": ["end2"],
            "end1": [],
            "end2": [],
            "isolated": [],
        }

        result = handler._find_reachable_transitions(["start"], forward_graph)
        expected = {"start", "middle1", "middle2", "end1", "end2"}
        assert result == expected

    def test_find_reachable_transitions_multiple_starts(self, handler):
        """Test finding reachable transitions from multiple starting points."""
        forward_graph = {
            "start1": ["middle1"],
            "start2": ["middle2"],
            "middle1": ["end1"],
            "middle2": ["end2"],
            "end1": [],
            "end2": [],
        }

        result = handler._find_reachable_transitions(
            ["start1", "start2"], forward_graph
        )
        expected = {"start1", "start2", "middle1", "middle2", "end1", "end2"}
        assert result == expected

    def test_find_reachable_transitions_with_cycles(self, handler):
        """Test finding reachable transitions with cycles (should not infinite loop)."""
        forward_graph = {
            "a": ["b"],
            "b": ["c"],
            "c": ["a", "d"],  # Cycle back to a
            "d": [],
        }

        result = handler._find_reachable_transitions(["a"], forward_graph)
        expected = {"a", "b", "c", "d"}
        assert result == expected

    def test_find_reachable_transitions_empty_start(self, handler):
        """Test finding reachable transitions with empty start list."""
        forward_graph = {"a": ["b"], "b": []}
        result = handler._find_reachable_transitions([], forward_graph)
        assert result == set()

    def test_remove_unreachable_waiting_transitions_basic_scenario(
        self, handler, sample_conditional_transition, sample_dependency_map
    ):
        """Test the main removal logic with the basic edge case scenario."""
        # Mock state manager
        mock_state_manager = Mock()
        mock_state_manager.waiting_transitions = {"node_y", "path_b", "other_waiting"}

        # Routing decision: conditional chose path_a (so path_b and default_path are unchosen)
        routing_decision = {
            "target_transition": "path_a",
            "matched_condition": 1,
            "condition_result": True,
        }

        # Call the main method
        result = handler._remove_unreachable_waiting_transitions(
            sample_conditional_transition,
            routing_decision,
            mock_state_manager,
            sample_dependency_map,
        )

        # Should remove path_b (unreachable) but keep node_y (has alternative path through node_x)
        # and other_waiting (not related to conditional)
        expected_removed = ["path_b"]  # path_b is only reachable through unchosen path
        assert sorted(result) == sorted(expected_removed)

    def test_remove_unreachable_waiting_transitions_no_waiting_affected(
        self, handler, sample_conditional_transition, sample_dependency_map
    ):
        """Test removal when no waiting transitions are affected."""
        # Mock state manager with no affected waiting transitions
        mock_state_manager = Mock()
        mock_state_manager.waiting_transitions = {"unrelated_transition"}

        routing_decision = {
            "target_transition": "path_a",
            "matched_condition": 1,
            "condition_result": True,
        }

        result = handler._remove_unreachable_waiting_transitions(
            sample_conditional_transition,
            routing_decision,
            mock_state_manager,
            sample_dependency_map,
        )

        assert result == []

    def test_remove_unreachable_waiting_transitions_multiple_paths_preserved(
        self, handler
    ):
        """Test that transitions reachable through multiple paths are preserved."""
        # Create a scenario where a transition has multiple paths to reach it
        transition = {
            "id": "conditional_node",
            "node_info": {
                "tools_to_use": [
                    {
                        "tool_name": "conditional",
                        "tool_parameters": {
                            "conditions": [
                                {
                                    "condition": "input.value > 10",
                                    "next_transition": "path_a",
                                },
                                {
                                    "condition": "input.value <= 10",
                                    "next_transition": "path_b",
                                },
                            ]
                        },
                    }
                ]
            },
        }

        dependency_map = {
            "conditional_node": [],
            "path_a": ["conditional_node"],
            "path_b": ["conditional_node"],
            "multi_path_node": [
                "path_a",
                "independent_node",
            ],  # Reachable through both chosen and independent paths
            "independent_node": [],
        }

        mock_state_manager = Mock()
        mock_state_manager.waiting_transitions = {"multi_path_node", "path_b"}

        routing_decision = {
            "target_transition": "path_a",  # path_b is unchosen
            "matched_condition": 1,
            "condition_result": True,
        }

        result = handler._remove_unreachable_waiting_transitions(
            transition, routing_decision, mock_state_manager, dependency_map
        )

        # Should remove path_b but preserve multi_path_node (has alternative route)
        assert "path_b" in result
        assert "multi_path_node" not in result
