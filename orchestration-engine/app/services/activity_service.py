import asyncio
import enum
import json
from typing import Any, Dict, Optional
import httpx
from app.config.config import settings
from app.utils.enhanced_logger import get_logger

logger = get_logger("ActivityService")


class ActivityType(enum.Enum):
    WORKFLOW = "WORKFLOW"
    AGENT = "AGENT"
    # Add other types as needed


class ActivityStatus(enum.Enum):
    STARTED = "ACTIVITY_STATUS_STARTED"
    IN_PROGRESS = "ACTIVITY_STATUS_IN_PROGRESS"
    COMPLETED = "ACTIVITY_STATUS_COMPLETED"
    FAILED = "ACTIVITY_STATUS_FAILED"
    PAUSED = "ACTIVITY_STATUS_PAUSED"
    CANCELLED = "ACTIVITY_STATUS_CANCELLED"


class LogType(enum.Enum):
    TIME_LOG = "TIME_LOG"
    RESULT_LOG = "RESULT_LOG"


class LogStatus(enum.Enum):
    SUCCESS = "LOG_STATUS_SUCCESS"
    FAILURE = "LOG_STATUS_FAILURE"
    PAUSED = "LOG_STATUS_PAUSED"
    APPROVED = "LOG_STATUS_APPROVED"
    CANCELLED = "LOG_STATUS_CANCELLED"


class ActivityService:
    """
    Service for tracking workflow activities via the API Gateway.

    This service provides methods to track workflow lifecycle events
    by making HTTP calls to the activity tracking API endpoints.
    """

    def __init__(self):
        self.api_gateway_url = settings.api_gateway_url
        self.auth_key = settings.orchestration_server_auth_key
        self.logger = logger

        # Validate required configuration
        if not self.api_gateway_url:
            self.logger.warning(
                "API Gateway URL not configured - activity tracking will be disabled"
            )
        if not self.auth_key:
            self.logger.warning(
                "Orchestration server auth key not configured - activity tracking may fail"
            )

    def _get_headers(self) -> Dict[str, str]:
        """Get the standard headers for API requests."""
        headers = {
            "Content-Type": "application/json",
        }

        if self.auth_key:
            headers["X-Server-Auth-Key"] = self.auth_key.get_secret_value()

        return headers

    async def create_activity(
        self,
        resource_id: str,
        user_id: str,
        activity_type: str = ActivityType.WORKFLOW.value,
        status: str = ActivityStatus.STARTED.value,
        correlation_id: Optional[str] = None,
        user_metadata: Optional[Dict[str, Any]] = None,
        timeout: int = 10,
    ) -> Optional[str]:
        """
        Create an activity record via the API Gateway.

        Args:
            resource_id: The ID of the resource (e.g., workflow execution ID)
            user_id: The ID of the user initiating the activity
            activity_type: Type of activity (default: "WORKFLOW")
            status: Status of the activity (default: "STARTED")
            correlation_id: Correlation ID for tracking (defaults to resource_id)
            user_metadata: Additional metadata to include
            timeout: Request timeout in seconds

        Returns:
            Optional[str]: Activity ID if successfully created, None otherwise
        """
        if not self.api_gateway_url:
            self.logger.debug(
                "Activity tracking skipped - API Gateway URL not configured"
            )
            return None

        if not user_id:
            self.logger.warning("Activity tracking skipped - user_id is required")
            return None

        # Use resource_id as correlation_id if not provided
        if not correlation_id:
            correlation_id = resource_id

        # Prepare the request payload
        payload = {
            "resource_id": resource_id,
            "user_id": user_id,
            "type": activity_type,
            "status": status,
            "correlation_id": correlation_id,
            "user_metadata": user_metadata or {},
        }

        url = f"{self.api_gateway_url}/api/v1/activities"
        headers = self._get_headers()

        try:
            self.logger.info(
                f"Creating activity: resource_id={resource_id}, user_id={user_id}, "
                f"type={activity_type}, status={status}"
            )

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url=url, json=payload, headers=headers)

                if response.status_code in [200, 201]:
                    try:
                        response_data = response.json()
                        self.logger.debug(f"Activity API response: {response_data}")

                        # Extract activity ID from response
                        activity_id = None
                        if "activity" in response_data and isinstance(
                            response_data["activity"], dict
                        ):
                            activity_id = response_data["activity"].get("id")

                        if activity_id:
                            self.logger.info(
                                f"Activity created successfully: {response.status_code} - {resource_id}, activity_id: {activity_id}"
                            )
                            return activity_id
                        else:
                            self.logger.warning(
                                f"Activity created but no activity ID found in response. Response: {response_data}"
                            )
                            return None
                    except json.JSONDecodeError as e:
                        self.logger.error(
                            f"Failed to parse JSON response: {str(e)} - Raw response: {response.text}"
                        )
                        return None
                    except Exception as e:
                        self.logger.error(
                            f"Unexpected error parsing activity response: {str(e)} - Response: {response.text}"
                        )
                        return None
                else:
                    self.logger.error(
                        f"Failed to create activity: {response.status_code} - {response.text}"
                    )
                    return None

        except httpx.TimeoutException:
            self.logger.error(
                f"Timeout creating activity for resource_id: {resource_id}"
            )
            return None
        except httpx.RequestError as e:
            self.logger.error(
                f"Request error creating activity for resource_id {resource_id}: {str(e)}"
            )
            return None
        except Exception as e:
            self.logger.error(
                f"Unexpected error creating activity for resource_id {resource_id}: {str(e)}"
            )
            return None

    def create_activity_async(
        self,
        resource_id: str,
        user_id: str,
        activity_type: str = "WORKFLOW",
        status: str = "STARTED",
        correlation_id: Optional[str] = None,
        user_metadata: Optional[Dict[str, Any]] = None,
        timeout: int = 10,
    ) -> None:
        """
        Create an activity record asynchronously without blocking.

        This is a fire-and-forget method that creates an async task
        to track the activity without waiting for the result.

        Args:
            resource_id: The ID of the resource (e.g., workflow execution ID)
            user_id: The ID of the user initiating the activity
            activity_type: Type of activity (default: "WORKFLOW")
            status: Status of the activity (default: "STARTED")
            correlation_id: Correlation ID for tracking (defaults to resource_id)
            user_metadata: Additional metadata to include
            timeout: Request timeout in seconds
        """

        async def _create_activity_task():
            """Internal task wrapper for async activity creation."""
            try:
                await self.create_activity(
                    resource_id=resource_id,
                    user_id=user_id,
                    activity_type=activity_type,
                    status=status,
                    correlation_id=correlation_id,
                    user_metadata=user_metadata,
                    timeout=timeout,
                )
            except Exception as e:
                self.logger.error(f"Error in async activity creation task: {str(e)}")

        # Create the task without awaiting it (fire-and-forget)
        asyncio.create_task(_create_activity_task())
        self.logger.debug(
            f"Async activity creation task started for resource_id: {resource_id}"
        )

    async def create_activity_log(
        self,
        activity_id: str,
        user_id: str,
        log_type: str = LogType.RESULT_LOG.value,
        log_status: str = LogStatus.SUCCESS.value,
        log_details: Optional[Dict[str, Any]] = None,
        activity_status: str = "ACTIVITY_STATUS_IN_PROGRESS",
        timeout: int = 10,
    ) -> bool:
        """
        Create an activity log record via the API Gateway.

        Args:
            activity_id: The ID of the activity to log for
            user_id: The ID of the user
            log_type: Type of log (default: "TOOL_EXECUTION")
            log_status: Status of the log (default: "SUCCESS")
            log_details: Additional details to include in the log
            activity_status: Status of the overall activity (default: "IN_PROGRESS")
            timeout: Request timeout in seconds

        Returns:
            bool: True if the activity log was successfully created, False otherwise
        """
        if not self.api_gateway_url:
            self.logger.debug(
                "Activity log tracking skipped - API Gateway URL not configured"
            )
            return False

        if not activity_id:
            self.logger.warning(
                "Activity log tracking skipped - activity_id is required"
            )
            return False

        if not user_id:
            self.logger.warning("Activity log tracking skipped - user_id is required")
            return False

        # Prepare the request payload
        payload = {
            "activity_id": activity_id,
            "user_id": user_id,
            "log_type": log_type,
            "log_status": log_status,
            "log_details": log_details or {},
            "activity_status": activity_status,
        }

        url = f"{self.api_gateway_url}/api/v1/activities/logs"
        headers = self._get_headers()

        try:
            self.logger.info(
                f"Creating activity log: activity_id={activity_id}, user_id={user_id}, "
                f"log_type={log_type}, log_status={log_status}, activity_status={activity_status}"
            )

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url=url, json=payload, headers=headers)

                if response.status_code in [200, 201]:
                    try:
                        response_data = response.json()
                        self.logger.debug(f"Activity log API response: {response_data}")
                        self.logger.info(
                            f"Activity log created successfully: {response.status_code} - {activity_id}"
                        )
                        return True
                    except json.JSONDecodeError as e:
                        self.logger.warning(
                            f"Activity log created but failed to parse response: {str(e)} - Raw response: {response.text}"
                        )
                        return True  # Still consider it successful if status code is 200/201
                else:
                    self.logger.error(
                        f"Failed to create activity log: {response.status_code} - {response.text}"
                    )
                    return False

        except httpx.TimeoutException:
            self.logger.error(
                f"Timeout creating activity log for activity_id: {activity_id}"
            )
            return False
        except httpx.RequestError as e:
            self.logger.error(
                f"Request error creating activity log for activity_id {activity_id}: {str(e)}"
            )
            return False
        except Exception as e:
            self.logger.error(
                f"Unexpected error creating activity log for activity_id {activity_id}: {str(e)}"
            )
            return False

    def create_activity_log_async(
        self,
        activity_id: str,
        user_id: str,
        log_type: str = LogType.RESULT_LOG.value,
        log_status: str = LogStatus.SUCCESS.value,
        log_details: Optional[Dict[str, Any]] = None,
        activity_status: str = ActivityStatus.IN_PROGRESS.value,
        timeout: int = 10,
    ) -> None:
        """
        Create an activity log record asynchronously without blocking.

        This is a fire-and-forget method that creates an async task
        to track the activity log without waiting for the result.

        Args:
            activity_id: The ID of the activity to log for
            user_id: The ID of the user
            log_type: Type of log (default: "TOOL_EXECUTION")
            log_status: Status of the log (default: "SUCCESS")
            log_details: Additional details to include in the log
            activity_status: Status of the overall activity (default: "IN_PROGRESS")
            timeout: Request timeout in seconds
        """

        async def _create_activity_log_task():
            """Internal task wrapper for async activity log creation."""
            try:
                await self.create_activity_log(
                    activity_id=activity_id,
                    user_id=user_id,
                    log_type=log_type,
                    log_status=log_status,
                    log_details=log_details,
                    activity_status=activity_status,
                    timeout=timeout,
                )
            except Exception as e:
                self.logger.error(
                    f"Error in async activity log creation task: {str(e)}"
                )

        # Create the task without awaiting it (fire-and-forget)
        asyncio.create_task(_create_activity_log_task())
        self.logger.debug(
            f"Async activity log creation task started for activity_id: {activity_id}"
        )

    @staticmethod
    def map_workflow_status_to_activity_status(workflow_status: str) -> str:
        """
        Map workflow status to activity status enum.

        Args:
            workflow_status: The workflow status from result_info

        Returns:
            str: Corresponding activity status
        """
        status_mapping = {
            "running": ActivityStatus.IN_PROGRESS.value,
            "completed": ActivityStatus.COMPLETED.value,
            "failed": ActivityStatus.FAILED.value,
            "waiting_for_approval": ActivityStatus.PAUSED.value,
            "cancelled": ActivityStatus.CANCELLED.value,
        }
        return status_mapping.get(workflow_status, ActivityStatus.IN_PROGRESS.value)

    @staticmethod
    def map_result_status_to_log_type(status: str) -> str:
        """
        Map result status to log type enum.

        Args:
            status: The status from result_info

        Returns:
            str: Corresponding log type
        """
        type_mapping = {
            "time_logged": LogType.TIME_LOG.value,
            "completed": LogType.RESULT_LOG.value,
        }
        return type_mapping.get(status, LogType.RESULT_LOG.value)

    @staticmethod
    def map_result_status_to_log_status(status: str) -> str:
        """
        Map result status to log status enum.

        Args:
            status: The status from result_info

        Returns:
            str: Corresponding log status
        """
        status_mapping = {
            "completed": LogStatus.SUCCESS.value,
            "complete": LogStatus.SUCCESS.value,  # Handle 'complete' status from final workflow events
            "started": LogStatus.SUCCESS.value,
            "connecting": LogStatus.SUCCESS.value,
            "time_logged": LogStatus.SUCCESS.value,
            "error": LogStatus.FAILURE.value,
            "failed": LogStatus.FAILURE.value,
            "paused": LogStatus.PAUSED.value,
            "approved": LogStatus.APPROVED.value,
            "cancelled": LogStatus.CANCELLED.value,
        }
        return status_mapping.get(status, LogStatus.SUCCESS.value)
