import asyncio
import json
import traceback
from typing import Any, Dict, List, Optional, Tuple
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer, ConsumerRecord  # type: ignore
from app.core_.executor_core import EnhancedWorkflowEngine
from app.config.config import settings
from app.services.initialize_workflow import initialize_workflow_with_params
from app.services.fetch_workflow import fetch_workflow_orchestration
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor
from app.services.agent_executor import AgentExecutor
from app.services.workflow_executor import WorkflowExecutor
from app.services.db_connections.db_initializer import (
    initialize_db_connections,
    close_db_connections,
)
from app.services.activity_service import ActivityService, ActivityType, ActivityStatus

logger = get_logger("KafkaWorkflowConsumer")


class KafkaWorkflowConsumer:

    def __init__(self) -> None:
        self.logger = logger
        self.kafka_broker: str = settings.kafka_bootstrap_servers
        self.kafka_workflow_request_topic: str = settings.kafka_workflow_request_topic
        self.execution_request_topic: str = settings.kafka_execution_request_topic
        self.approval_request_topic: str = settings.kafka_approval_request_topic
        self.workflow_execution_request_topic: str = (
            settings.kafka_workflow_execution_request_topic
        )
        self.group_id: str = settings.kafka_main_consumer_group_id
        self.consumer: AIOKafkaConsumer = AIOKafkaConsumer(
            self.kafka_workflow_request_topic,
            self.execution_request_topic,
            self.approval_request_topic,
            self.workflow_execution_request_topic,
            bootstrap_servers=self.kafka_broker,
            group_id=self.group_id,
            session_timeout_ms=30000,
            max_poll_interval_ms=60000,
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )
        self.producer: AIOKafkaProducer = AIOKafkaProducer(
            bootstrap_servers=self.kafka_broker,
            max_request_size=524288000,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        self.mcp_tool_executor = KafkaToolExecutor(producer=self.producer)
        self.node_executor = NodeExecutor(producer=self.producer)
        self.agent_executor = AgentExecutor(producer=self.producer)
        self.workflow_executor = WorkflowExecutor(producer=self.producer)
        self.activity_service = ActivityService()
        self.running_workflows: Dict[
            str, Tuple[EnhancedWorkflowEngine, asyncio.Task]
        ] = {}
        self.approval_wait_queue: Dict[str, str] = {}
        # Store activity IDs and user IDs for workflow executions
        self.workflow_activity_ids: Dict[str, str] = {}  # correlation_id -> activity_id
        self.workflow_user_ids: Dict[str, str] = {}  # correlation_id -> user_id

        # Initialize database connections once at server startup
        self.logger.info("Initializing database connections...")
        self.db_connections = initialize_db_connections()
        self.logger.info("Database connections initialized successfully")

        # Initialize a WorkflowStateManager to set the reference in RedisEventListener
        # This ensures the RedisEventListener has a proper reference for archival operations
        self._initialize_workflow_state_manager_reference()

        self.logger.info("KafkaWorkflowConsumer initialized successfully")

    def _initialize_workflow_state_manager_reference(self):
        """
        Initialize a temporary WorkflowStateManager to set the reference in RedisEventListener.
        This ensures the RedisEventListener has a proper reference for archival operations.
        """
        try:
            from app.core_.state_manager import WorkflowStateManager

            # Create a temporary WorkflowStateManager instance to set the reference
            # The constructor automatically sets the reference in RedisEventListener
            temp_state_manager = WorkflowStateManager(  # noqa: F841
                workflow_id="temp_initialization", db_connections=self.db_connections
            )

            self.logger.info(
                "WorkflowStateManager reference set in RedisEventListener for archival operations"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to initialize WorkflowStateManager reference: {e}"
            )
            # This is not critical for startup, so we continue

    async def start_consumer(self) -> None:
        await self.consumer.start()
        await self.producer.start()
        await self.mcp_tool_executor.start()
        await self.node_executor.start()
        await self.agent_executor.start()
        await self.workflow_executor.start()
        try:
            # Main consumer loop
            while True:
                try:
                    msg = await self.consumer.getone()
                    topic: str = msg.topic
                    self.logger.info(
                        f"Received: topic={topic}, partition={msg.partition}, offset={msg.offset}"
                    )

                    try:
                        await self.process_message(msg, topic)
                    except Exception as e:
                        # Catch any exceptions from message processing
                        self.logger.error(f"Error processing message: {str(e)}")
                        correlation_id = "unknown"
                        reply_topic = ""

                        # Try to extract correlation_id and reply_topic from message headers
                        try:
                            decoded_headers = self.decode_headers(msg.headers)
                            correlation_id = decoded_headers.get(
                                "correlationId", "unknown"
                            )
                            reply_topic = decoded_headers.get("reply-topic", "")
                        except Exception as header_e:
                            self.logger.error(
                                f"Error extracting headers: {str(header_e)}"
                            )

                        # Try to send error response if we have a reply topic
                        if reply_topic:
                            try:
                                await self.send_error_response(
                                    reply_topic,
                                    correlation_id,
                                    f"Error processing message: {str(e)}",
                                    0,
                                )
                            except Exception as resp_e:
                                self.logger.error(
                                    f"Error sending error response: {str(resp_e)}"
                                )

                        # Always commit the offset even if processing failed
                        try:
                            await self.consumer.commit()
                            self.logger.info(
                                f"Committed offset after error: {msg.offset}"
                            )
                        except Exception as commit_e:
                            self.logger.error(
                                f"Error committing offset: {str(commit_e)}"
                            )

                except asyncio.CancelledError:
                    # Proper cancellation
                    raise
                except Exception as e:
                    # Catch any consumer.getone() failures or other loop-level errors
                    self.logger.error(f"Error in consumer loop: {str(e)}")
                    # Brief delay before retrying to avoid tight error loops
                    await asyncio.sleep(1)
        except asyncio.CancelledError:
            self.logger.info("Consumer task cancelled.")
        finally:
            try:
                await self.consumer.stop()
                await self.producer.stop()
                await self.mcp_tool_executor.stop()
                await self.node_executor.stop()
                await self.agent_executor.stop()

                # Close database connections
                self.logger.info("Closing database connections...")
                close_db_connections()
                self.logger.info("Database connections closed")

                self.logger.info("Consumer and producer stopped gracefully.")
            except Exception as e:
                self.logger.error(f"Error during consumer/producer cleanup: {str(e)}")

                # Try to close database connections even if other cleanup failed
                try:
                    close_db_connections()
                    self.logger.info(
                        "Database connections closed during error handling"
                    )
                except Exception as db_e:
                    self.logger.error(
                        f"Error closing database connections: {str(db_e)}"
                    )

                raise Exception(
                    f"Error during consumer/producer cleanup: {str(e)}"
                ) from e

    async def stop_consumer(self) -> None:
        if self.consumer:
            await self.consumer.stop()
        if self.producer:
            await self.producer.stop()
        if self.mcp_tool_executor.consumer:
            await self.mcp_tool_executor.stop()
        if self.node_executor.consumer:
            await self.node_executor.stop()
        if self.agent_executor._consumer:
            await self.agent_executor.stop()

        # Close database connections
        self.logger.info("Closing database connections...")
        close_db_connections()
        self.logger.info("Database connections closed")

        self.logger.info("Consumer and producer stopped.")

    async def process_message(self, msg: ConsumerRecord, topic: str) -> None:
        if topic == self.kafka_workflow_request_topic:
            await self.process_workflow_request(msg.value, msg, topic=topic)
        elif topic == self.workflow_execution_request_topic:
            await self.process_workflow_request(msg.value, msg, topic=topic)
        elif topic == self.execution_request_topic:
            await self.process_execution_request(msg.value, msg)
        elif topic == self.approval_request_topic:
            await self.process_approval_request(msg.value, msg)
        else:
            self.logger.error(f"⚠️ Unknown topic: {topic}")
            await self.consumer.commit()

    async def send_response(
        self, reply_topic: str, correlation_id: str, response_data: Dict[str, Any]
    ) -> None:
        headers: List[tuple[str, bytes]] = [
            ("correlationId", correlation_id.encode("utf-8"))
        ]
        await self.producer.send(
            reply_topic,
            response_data,
            headers=headers,
        )

        self.logger.info(
            f"Sent response to {reply_topic} with correlation ID: {correlation_id}, "
            f"response: {response_data}"
        )

    async def _collect_workflow_end_results(self, workflow_id: str) -> Any:
        """
        Collect results from all end transitions for a workflow.

        For workflows with multiple end transitions, returns a list of results.
        For workflows with a single end transition, returns the result directly.

        Args:
            workflow_id: The workflow ID (not correlation ID) of the workflow

        Returns:
            List of results for multiple end transitions, single result for one end transition,
            or None if no results found
        """
        if workflow_id not in self.running_workflows:
            self.logger.warning(
                f"No running workflow found for workflow_id: {workflow_id}"
            )
            return None

        engine, _ = self.running_workflows[workflow_id]
        if not hasattr(engine, "state_manager"):
            self.logger.warning(
                f"Workflow engine has no state_manager for workflow_id: {workflow_id}"
            )
            return None

        # Get all end transitions
        end_transitions = engine.state_manager.end_transitions
        if not end_transitions:
            self.logger.info(f"No end transitions defined for workflow {workflow_id}")
            return None

        # Collect results from all completed end transitions (final nodes, not intermediate workflows)
        end_transition_results = []
        for transition_id in end_transitions:
            result = engine.state_manager.get_transition_result(transition_id)
            if result is not None:
                # Extract the actual result value from the transition result wrapper
                # This should be the result from the final node (e.g., AI Agent, Document Generator, etc.)
                # NOT the result from intermediate workflow nodes
                if isinstance(result, dict) and "result" in result:
                    # Handle nested result structure from transition execution
                    nested_result = result["result"]
                    if isinstance(nested_result, dict) and "result" in nested_result:
                        actual_result = nested_result["result"]
                    else:
                        actual_result = nested_result
                else:
                    actual_result = result

                end_transition_results.append(actual_result)
                self.logger.info(
                    f"🔧 Collected END NODE result for transition {transition_id}: {actual_result}"
                )

        # Return based on number of results
        if not end_transition_results:
            self.logger.warning(
                f"No end transition results found for workflow {workflow_id}"
            )
            return None
        elif len(end_transition_results) == 1:
            # Single end transition - return result directly
            self.logger.info(
                f"Returning single end transition result for workflow {workflow_id}"
            )
            return end_transition_results[0]
        else:
            # Multiple end transitions - return as list
            self.logger.info(
                f"🔧 Multiple end transitions ({len(end_transition_results)}) - returning results as list for workflow {workflow_id}: {end_transition_results}"
            )
            return end_transition_results

    async def send_error_response(
        self,
        reply_topic: str,
        correlation_id: str,
        error_message: str,
        sequence_number: int,
    ) -> None:
        if reply_topic:
            await self.send_response(
                reply_topic,
                correlation_id,
                {
                    "status": "error",
                    "workflow_status": "failed",
                    "result": error_message,
                    "message": error_message,
                    "sequence": sequence_number,
                },
            )

    def decode_headers(self, headers_list: List[tuple[bytes, bytes]]) -> Dict[str, str]:
        decoded_headers: Dict[str, str] = {}
        for key, value in headers_list:
            key = key.decode("utf-8") if isinstance(key, bytes) else key
            value = value.decode("utf-8") if isinstance(value, bytes) else value
            decoded_headers[key] = value
        return decoded_headers

    async def handle_resume_result(
        self,
        task: asyncio.Task,
        correlation_id: str,
        reply_topic: str,
        sequence_number: int,
    ) -> None:
        resume_success: bool = await task
        if not resume_success:
            self.logger.error(
                f"Re-execute/Retry failed: Could not load state for workflow {correlation_id}"
            )
            await self.send_error_response(
                reply_topic,
                correlation_id,
                "Retry failed: Could not load state.",
                sequence_number,
            )
        else:
            self.logger.info(f"Workflow {correlation_id} resumed from saved state.")

    async def handle_regenerate_result(
        self,
        task: asyncio.Task,
        correlation_id: str,
        node_id: str,
        response_message_base: str,
        reply_topic: str,
    ) -> None:
        regenerate_success: bool = await task
        if regenerate_success:
            response_message: str = (
                response_message_base
                + f" Regenerate command sent to server '{node_id}'."
            )
        else:
            response_message = (
                response_message_base
                + f" Failed to regenerate server '{node_id}' (regeneration error)."
            )

        self.logger.info(response_message)
        await self.send_response(reply_topic, correlation_id, response_message)

    async def handle_workflow_result(
        self,
        execution_task: asyncio.Task,
        correlation_id: str,
        workflow_id: str,
        reply_topic: str,
    ) -> None:
        """
        Handle the workflow execution result and send appropriate response.
        This function will catch any exceptions from the task and report them.
        For workflows with multiple end transitions, collects and returns all end transition results.
        """
        execution_success: bool = False
        response_data: Dict[str, Any] = {
            "status": "complete",
            "result": "",
            "workflow_status": None,
        }

        try:
            # Await the execution task and catch any exceptions
            try:
                execution_success = await execution_task
            except asyncio.CancelledError:
                self.logger.warning(
                    f"Workflow execution for '{workflow_id}' was cancelled"
                )
                response_data["status"] = "Workflow Cancelled"
                response_data["workflow_status"] = "cancelled"
                response_data["result"] = (
                    f"Workflow '{workflow_id}' execution was cancelled"
                )
            except Exception as e:
                self.logger.error(f"Exception in workflow execution: {str(e)}")
                traceback.print_exc()
                response_data["status"] = "failed"
                response_data["workflow_status"] = "failed"
                response_data["result"] = (
                    f"Exception in workflow '{workflow_id}': {str(e)}"
                )
                response_data["error"] = str(e)
                response_data["error_type"] = type(e).__name__
            else:
                # Task completed without exception, check success status
                if execution_success:
                    response_message: str = (
                        f"Workflow '{workflow_id}' executed successfully."
                    )
                    response_data["workflow_status"] = "completed"

                    # 🔧 NEW: Collect actual results from end transitions for nested workflows
                    workflow_results = await self._collect_workflow_end_results(
                        workflow_id
                    )
                    if workflow_results is not None:
                        response_data["result"] = workflow_results
                        response_data["raw_result"] = workflow_results
                        self.logger.info(
                            f"🔧 Collected end transition results for workflow {workflow_id}: {workflow_results}"
                        )
                    else:
                        response_data["result"] = response_message
                else:
                    response_message = (
                        f"Failed to execute workflow '{workflow_id}' (execution error)."
                    )
                    response_data["workflow_status"] = "failed"
                    response_data["result"] = response_message

        except Exception as e:
            # Catch any exceptions in our exception handling code
            self.logger.error(f"Error in handling workflow result: {str(e)}")
            traceback.print_exc()
            response_data["status"] = "failed"
            response_data["workflow_status"] = "failed"
            response_data["result"] = f"Error handling workflow result: {str(e)}"
            response_data["error"] = str(e)
            response_data["error_type"] = type(e).__name__

        # Log the final outcome
        self.logger.info(
            f"Workflow '{workflow_id}' final status: {response_data['workflow_status']}, result: {response_data['result']}"
        )

        # Send the response
        try:
            # For workflow execution requests, add correlation_id to response payload
            if reply_topic == settings.kafka_workflow_execution_result_topic:
                response_data["correlation_id"] = correlation_id

            await self.send_response(reply_topic, correlation_id, response_data)
        except Exception as e:
            self.logger.error(f"Failed to send workflow result response: {str(e)}")

        # Clean up resources if needed
        try:
            if correlation_id in self.running_workflows:
                # del self.running_workflows[correlation_id]
                self.logger.debug(
                    f"Stopped workflow with correlation_id: {correlation_id} "
                )
        except Exception as e:
            self.logger.error(f"Error cleaning up workflow resources: {str(e)}")

    async def process_workflow_request(
        self,
        msg_value: bytes,
        msg: ConsumerRecord,
        action_type: str = "task-request",
        topic: str = None,
    ) -> bool:
        correlation_id: str = ""
        reply_topic: str = ""
        sequence_number: int = 0
        try:
            decoded_headers: Dict[str, str] = self.decode_headers(msg.headers)
            correlation_id = decoded_headers.get("correlationId", "")
            reply_topic = decoded_headers.get("reply-topic", "")
            reply_topic = decoded_headers.get("reply-topic", "")

            if not reply_topic or not correlation_id:
                self.logger.error(
                    "⚠️ Missing reply-topic or correlationId in headers for task-request. Skipping response."
                )
                return False

            # Determine if this is a workflow execution request (from workflow_executor)
            is_workflow_execution_request = (
                topic == self.workflow_execution_request_topic
            )

            # Track completion status to prevent infinite callback loops
            workflow_completed = False

            async def kafka_result_callback(
                result_info: Dict[str, Any], corr_id: str = correlation_id
            ) -> None:
                nonlocal sequence_number, workflow_completed

                # 🔧 PREVENT INFINITE LOOP: Stop calling callback after workflow completion
                if workflow_completed:
                    self.logger.debug(
                        f"🔧 Skipping callback for completed workflow {corr_id} - sequence {sequence_number}"
                    )
                    return

                result_info["sequence"] = sequence_number

                self.logger.info(
                    f"Task Result callback (sequence {sequence_number}, corr_id {corr_id}):",
                    result_info,
                )

                sequence_number += 1

                # 🔧 DETECT WORKFLOW COMPLETION: Mark workflow as completed to stop future callbacks
                if result_info.get("workflow_status") == "completed":
                    workflow_completed = True
                    self.logger.info(
                        f"🔧 Workflow {corr_id} completed - marking to prevent future callbacks"
                    )
                    # Note: Don't clean up activity IDs here yet - the final completion event still needs them

                if result_info.get("approval_required") == True:
                    self.logger.info(
                        f"Transition {result_info.get('transition_id')} requires approval. Workflow paused."
                    )
                    self.approval_wait_queue[corr_id] = result_info.get("transition_id")
                    result_info["workflow_status"] = "waiting_for_approval"
                else:
                    # Only set to "running" if not already completed
                    if not workflow_completed:
                        result_info["workflow_status"] = "running"

                # Use dedicated topic for workflow execution responses
                if is_workflow_execution_request:
                    response_topic = settings.kafka_workflow_execution_result_topic
                    # For workflow execution requests, add correlation_id to the response payload
                    result_info["correlation_id"] = corr_id
                else:
                    response_topic = reply_topic

                # Track activity log for this workflow event
                try:
                    activity_id = self.workflow_activity_ids.get(corr_id)
                    stored_user_id = self.workflow_user_ids.get(corr_id)
                    if activity_id and stored_user_id:
                        # Map statuses using the utility functions
                        log_type = self.activity_service.map_result_status_to_log_type(
                            result_info.get("status", "")
                        )
                        log_status = (
                            self.activity_service.map_result_status_to_log_status(
                                result_info.get("status", "")
                            )
                        )
                        activity_status = self.activity_service.map_workflow_status_to_activity_status(
                            result_info.get("workflow_status", "running")
                        )

                        # Create activity log with the entire result_info as log_details
                        self.activity_service.create_activity_log_async(
                            activity_id=activity_id,
                            user_id=stored_user_id,
                            log_type=log_type,
                            log_status=log_status,
                            log_details=result_info,
                            activity_status=activity_status,
                        )

                        self.logger.debug(
                            f"Activity log created for workflow: {corr_id}, activity_id: {activity_id}, "
                            f"log_type: {log_type}, log_status: {log_status}, activity_status: {activity_status}"
                        )

                        # Clean up activity IDs after final completion event
                        if result_info.get(
                            "workflow_status"
                        ) == "completed" and result_info.get("status") in [
                            "complete",
                            "completed",
                        ]:
                            self.workflow_activity_ids.pop(corr_id, None)
                            self.workflow_user_ids.pop(corr_id, None)
                            self.logger.debug(
                                f"Cleaned up activity tracking data for completed workflow: {corr_id}"
                            )
                    else:
                        self.logger.debug(
                            f"No activity ID found for workflow: {corr_id} - skipping activity log"
                        )
                except Exception as activity_log_error:
                    # Log the error but don't fail the workflow
                    self.logger.error(
                        f"Failed to create activity log for workflow {corr_id}: {str(activity_log_error)}"
                    )

                await self.send_response(response_topic, corr_id, result_info)

            # Parse the message
            try:
                msg_json: Dict[str, Any] = json.loads(msg_value.decode("utf-8"))
                self.logger.debug(f"message json: {msg_json}")
            except json.JSONDecodeError as e:
                self.logger.error(f"Invalid JSON format in task-request: {str(e)}")
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    f"Invalid JSON format: {str(e)}",
                    sequence_number,
                )
                return False
            except UnicodeDecodeError as e:
                self.logger.error(f"Failed to decode message: {str(e)}")
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    f"Message decoding error: {str(e)}",
                    sequence_number,
                )
                return False

            try:
                workflow_json_content: Dict[str, Any] = msg_json["data"]
                workflow_id: str = workflow_json_content.get("workflow_id", "")
                user_id: str = workflow_json_content.get("user_id", "")

                if user_id:
                    self.logger.info(
                        f"Extracted user_id: {user_id} for workflow: {workflow_id}"
                    )
                else:
                    self.logger.warning(
                        f"No user_id found in workflow request for workflow: {workflow_id}"
                    )
            except KeyError as e:
                self.logger.error(f"Missing required field in message: {str(e)}")
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    f"Missing required field: {str(e)}",
                    sequence_number,
                )
                return False

            # Load workflow
            try:
                # Fetch the workflow from the API
                if workflow_id == "ciny":
                    with open(
                        "E:/RapidInnovation/Automation Projects/Ruh/ruh.ai/workflow-service/testing/converted_schema.json",
                        "r",
                    ) as file:
                        workflow = json.load(file)
                else:
                    workflow = fetch_workflow_orchestration(workflow_id)

                # Note: URL processing has been removed - server_script_path is now optional
                self.logger.debug(
                    f"Workflow loaded for {workflow_id} - server_script_path is optional"
                )
            except Exception as e:
                error_msg = f"Failed to fetch workflow: {str(e)}"
                self.logger.error(error_msg)
                await self.send_error_response(
                    reply_topic, correlation_id, error_msg, sequence_number
                )
                return False
            except json.JSONDecodeError as e:
                error_msg = f"Invalid workflow definition JSON: {str(e)}"
                self.logger.error(error_msg)
                await self.send_error_response(
                    reply_topic, correlation_id, error_msg, sequence_number
                )
                return False

            # Initialize workflow
            try:
                init_workflow = initialize_workflow_with_params(
                    workflow, workflow_json_content
                )
            except Exception as e:
                error_msg = f"Failed to initialize workflow with params: {str(e)}"
                self.logger.error(error_msg)
                await self.send_error_response(
                    reply_topic, correlation_id, error_msg, sequence_number
                )
                return False

            node_id: Optional[str] = msg_json.get("server_id", None)
            params: Optional[Dict[str, Any]] = msg_json.get("params", None)
            approval: bool = msg_json.get("approval", False)

            # Initialize workflow engine
            try:
                engine = EnhancedWorkflowEngine(
                    init_workflow=init_workflow,
                    tool_executor=self.mcp_tool_executor,
                    node_executor=self.node_executor,
                    agent_executor=self.agent_executor,
                    workflow_executor=self.workflow_executor,
                    result_callback=kafka_result_callback,
                    workflow_id=correlation_id,
                    approval=approval,
                    db_connections=self.db_connections,
                    user_id=user_id,
                )
            except Exception as e:
                error_msg = f"Engine initialization error: {str(e)}"
                self.logger.error(error_msg)
                traceback.print_exc()  # Print detailed stack trace
                await self.send_error_response(
                    reply_topic, correlation_id, error_msg, sequence_number
                )
                return False

            response_message: str = ""
            if action_type in {"retry", "re-execute"}:
                try:
                    resume_task: asyncio.Task = asyncio.create_task(
                        engine.resume_workflow_from_state()
                    )

                    asyncio.create_task(
                        self.handle_resume_result(
                            resume_task, correlation_id, reply_topic, sequence_number
                        )
                    )
                    response_message_base: str = (
                        f"Workflow re-execution initiated from saved state ({action_type})."
                    )
                    if action_type == "re-execute" and node_id:
                        self.logger.info(
                            f"Re-execute request includes regenerate server '{node_id}', workflow: {correlation_id}, params: {params}"
                        )
                        try:
                            regenerate_task: asyncio.Task = asyncio.create_task(
                                engine.transition_handler.regenerate_transition(
                                    node_id,
                                    action_type=action_type,
                                    transition_id=node_id,
                                    server_params_override=params if params else None,
                                )
                            )
                            self.running_workflows[correlation_id] = (
                                engine,
                                regenerate_task,
                            )

                            asyncio.create_task(
                                self.handle_regenerate_result(
                                    regenerate_task,
                                    correlation_id,
                                    node_id,
                                    response_message_base,
                                    reply_topic,
                                )
                            )
                        except Exception as e:
                            error_msg = f"Failed to start regenerate task: {str(e)}"
                            self.logger.error(error_msg)
                            await self.send_error_response(
                                reply_topic, correlation_id, error_msg, sequence_number
                            )
                            # Continue execution despite regeneration failure
                except Exception as e:
                    error_msg = f"Failed to resume workflow: {str(e)}"
                    self.logger.error(error_msg)
                    await self.send_error_response(
                        reply_topic, correlation_id, error_msg, sequence_number
                    )
                    return False
            else:
                response_message = "Workflow Initialized"
                try:
                    # Track workflow activity and store activity ID BEFORE starting execution
                    try:
                        user_metadata = {
                            "workflow_id": workflow_id,
                            "approval_required": approval,
                            "execution_mode": action_type,
                        }
                        activity_id = await self.activity_service.create_activity(
                            resource_id=correlation_id,
                            user_id=user_id,
                            activity_type=ActivityType.WORKFLOW.value,
                            status=ActivityStatus.STARTED.value,
                            correlation_id=correlation_id,
                            user_metadata=user_metadata,
                        )
                        if activity_id:
                            self.workflow_activity_ids[correlation_id] = activity_id
                            self.workflow_user_ids[correlation_id] = user_id
                            self.logger.debug(
                                f"Activity tracking initiated for workflow: {correlation_id}, activity_id: {activity_id}"
                            )
                        else:
                            self.logger.warning(
                                f"Activity tracking failed - no activity ID returned for workflow: {correlation_id}"
                            )
                    except Exception as activity_error:
                        # Log the error but don't fail the workflow
                        self.logger.error(
                            f"Failed to track workflow activity: {str(activity_error)}"
                        )
                        # Continue with workflow execution regardless of activity tracking failure

                    # Start execution
                    execution_task: asyncio.Task = asyncio.create_task(engine.execute())
                    self.running_workflows[correlation_id] = (engine, execution_task)
                    # Create a task to monitor execution
                    asyncio.create_task(
                        self.handle_workflow_result(
                            execution_task, correlation_id, workflow_id, reply_topic
                        )
                    )

                    self.logger.info(
                        f"Workflow execution started in background for {action_type}, corr_id: {correlation_id}"
                    )
                except Exception as e:
                    error_msg = f"Failed to start workflow execution: {str(e)}"
                    self.logger.error(error_msg)
                    traceback.print_exc()
                    await self.send_error_response(
                        reply_topic, correlation_id, error_msg, sequence_number
                    )
                    return False

            # Send initialization success response
            await self.send_response(
                reply_topic,
                correlation_id,
                {
                    "workflow_id": workflow_id,
                    "status": "Initialized",
                    "message": response_message,
                    "result": response_message,
                    "workflow_status": "running",
                },
            )

            # Commit the offset
            try:
                await self.consumer.commit()
                self.logger.info(
                    f"Committed offset after starting engine for {action_type}: {msg.offset}, corr_id: {correlation_id}"
                )
            except Exception as e:
                self.logger.error(f"Failed to commit offset: {str(e)}")
                # Don't return False here, as the workflow has already been started

            return True

        except Exception as e:
            self.logger.error(f"Unexpected error processing task-request: {str(e)}")
            traceback.print_exc()  # Print detailed stack trace

            # Ensure we have a correlation_id and reply_topic before trying to send a response
            if correlation_id and reply_topic:
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    f"Workflow initialization error: {str(e)}",
                    sequence_number,
                )

            return False

    async def process_execution_request(
        self, msg_value: bytes, msg: ConsumerRecord
    ) -> bool:
        try:
            msg_json: Dict[str, Any] = json.loads(msg_value.decode("utf-8"))
            decoded_headers: Dict[str, str] = self.decode_headers(msg.headers)
            correlation_id: str = decoded_headers.get("correlationId", "")
            reply_topic: str = decoded_headers.get("reply-topic", "")

            if not correlation_id:
                self.logger.error(
                    "⚠️ Missing correlationId in headers for execution-request."
                )
                return False

            action_type: str = msg_json.get("action", "")
            node_id: Optional[str] = msg_json.get("node_id", None)
            params: Optional[Dict[str, Any]] = msg_json.get("params", None)

            if action_type == "re-execute":
                return await self.process_workflow_request(
                    msg_value,
                    msg,
                    action_type="re-execute",
                    topic=self.execution_request_topic,
                )

            if action_type == "retry":
                return await self.process_workflow_request(
                    msg_value,
                    msg,
                    action_type="retry",
                    topic=self.execution_request_topic,
                )

            elif action_type == "regenerate":
                engine_tuple: Optional[Tuple[EnhancedWorkflowEngine, asyncio.Task]] = (
                    self.running_workflows.get(correlation_id)
                )
                if not engine_tuple:
                    self.logger.error(
                        f"No running workflow found for correlationId: {correlation_id} (execution-request)"
                    )
                    if reply_topic:
                        await self.send_error_response(
                            reply_topic,
                            correlation_id,
                            "No workflow found for execution request",
                            0,
                        )
                    return False

                engine, execution_task = engine_tuple
                if node_id:
                    self.logger.info(
                        f"Execution request: regenerate server '{node_id}', workflow: {correlation_id}, params: {params}"
                    )
                    regenerate_success: bool = (
                        await engine.transition_handler.regenerate_transition(
                            transition_id=node_id,
                            action_type="regenerate",
                            server_params_override=params if params else None,
                        )
                    )
                    if regenerate_success:
                        response_msg: str = (
                            f"Regenerate command sent to server '{node_id}'"
                        )
                        result: bool = True
                    else:
                        response_msg = f"Failed to regenerate server '{node_id}' (regeneration error)."
                        result = False
                else:
                    response_msg = "Node ID is required for regenerate action."
                    result = False
            else:
                response_msg = (
                    f"Invalid action type '{action_type}' in execution request"
                )
                result = False

            if reply_topic:
                await self.send_response(
                    reply_topic,
                    correlation_id,
                    {
                        "status": "Execution Request Processed",
                        "result": response_msg,
                        "action": action_type,
                        "node_id": node_id,
                        "request_params": params,
                    },
                )

            await self.consumer.commit()
            self.logger.info(
                f"Committed offset after processing execution-request: {msg.offset}, corr_id: {correlation_id}"
            )
            return result
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON format in execution-request: {str(e)}")
            if reply_topic:
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    "Invalid JSON format in execution request",
                    0,
                )

            return False
        except Exception as e:
            self.logger.error(f"Error processing execution-request: {str(e)}")
            if reply_topic:
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    f"Execution request error: {str(e)}",
                    0,
                )

            return False

    async def process_approval_request(
        self, msg_value: bytes, msg: ConsumerRecord
    ) -> bool:
        try:
            msg_json: Dict[str, Any] = json.loads(msg_value.decode("utf-8"))
            decoded_headers: Dict[str, str] = self.decode_headers(msg.headers)
            correlation_id: str = decoded_headers.get("correlationId", "")
            reply_topic: str = decoded_headers.get("reply-topic", "")

            if not correlation_id:
                self.logger.error(
                    "⚠️ Missing correlationId in headers for approval-request."
                )
                return False

            engine_tuple: Optional[Tuple[EnhancedWorkflowEngine, asyncio.Task]] = (
                self.running_workflows.get(correlation_id)
            )
            if not engine_tuple:
                self.logger.error(
                    f"No running workflow found for correlationId: {correlation_id} (approval-request)"
                )
                if reply_topic:
                    await self.send_error_response(
                        reply_topic,
                        correlation_id,
                        "No workflow found for approval request",
                        0,
                    )
                return False
            engine, execution_task = engine_tuple
            approval_decision: str = msg_json.get("decision", "")

            self.logger.info(f"Approval request received for workflow {correlation_id}")

            if approval_decision.lower() == "approve":
                if correlation_id in self.approval_wait_queue:
                    transition_id_waiting_approval: str = self.approval_wait_queue[
                        correlation_id
                    ]
                    engine_tuple = self.running_workflows.get(correlation_id)
                    if not engine_tuple:
                        self.logger.error(
                            f"No running workflow found for correlationId: {correlation_id} (approval-request)"
                        )
                        if reply_topic:
                            await self.send_error_response(
                                reply_topic,
                                correlation_id,
                                "No workflow found for approval request",
                                0,
                            )
                        return False
                    engine, execution_task = engine_tuple
                    if engine.state_manager.transitions_waiting_for_approval is {}:
                        del self.approval_wait_queue[correlation_id]

                    engine.transition_handler._pause_event.set()
                    response_msg: str = (
                        f"Approval granted for transition '{transition_id_waiting_approval}'. Workflow will proceed."
                    )
                    result: bool = True

                    if reply_topic:
                        await self.send_response(
                            reply_topic,
                            correlation_id,
                            {
                                "status": "approved",
                                "result": response_msg,
                                "decision": "approved",
                                "transition_id": transition_id_waiting_approval,
                                "workflow_status": "running",
                            },
                        )
                else:
                    response_msg = f"No workflow waiting for approval for correlationId '{correlation_id}' or approval already processed."
                    result = False
                    if reply_topic:
                        await self.send_response(
                            reply_topic,
                            correlation_id,
                            {
                                "status": "ignored",
                                "result": response_msg,
                                "decision": approval_decision,
                                "workflow_status": "unknown",
                            },
                        )
            elif approval_decision.lower() == "reject":
                response_msg: str = (
                    f"Workflow Cancellation requested. Workflow will be cancelled shortly."
                )
                execution_task.cancel()
                result: bool = True

                if reply_topic:
                    await self.send_response(
                        reply_topic,
                        correlation_id,
                        {
                            "status": "cancelled",
                            "result": response_msg,
                            "decision": "rejected",
                            "workflow_status": "cancelled",
                        },
                    )
            else:
                response_msg = f"Invalid approval decision: '{approval_decision}'. Expected 'approve' or 'reject'."
                result = False
                if reply_topic:
                    await self.send_response(
                        reply_topic,
                        correlation_id,
                        {
                            "status": "error",
                            "result": response_msg,
                            "decision": approval_decision,
                            "transition_id": transition_id_waiting_approval,
                            "workflow_status": "waiting_for_approval",
                        },
                    )

            await self.consumer.commit()
            self.logger.info(
                f"Committed offset after processing approval-request: {msg.offset}, corr_id: {correlation_id}"
            )
            return result

        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON format in approval-request: {str(e)}")
            if reply_topic:
                await self.send_error_response(
                    reply_topic,
                    correlation_id,
                    "Invalid JSON format in approval request",
                    0,
                )
            return False
        except Exception as e:
            self.logger.error(f"Error processing approval-request: {str(e)}")
            if reply_topic:
                await self.send_error_response(
                    reply_topic, correlation_id, f"Approval request error: {str(e)}", 0
                )
            return False


async def consume() -> None:
    consumer_instance = KafkaWorkflowConsumer()
    await consumer_instance.start_consumer()
