syntax = "proto3";

package authentication;


// Authentication Service
service AuthenticationService {
  // OAuth Flow Methods
  rpc InitiateOAuth(OAuthAuthorizeRequest) returns (OAuthAuthorizeResponse);
  rpc HandleOAuthCallback(OAuthCallbackRequest) returns (OAuthCallbackResponse);
  rpc RefreshOAuthTokens(OAuthRefreshRequest) returns (OAuthRefreshResponse);

  // Credential Management
  rpc GetOAuthCredentials(OAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc GetServerOAuthCredentials(ServerOAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc DeleteOAuthCredentials(DeleteOAuthCredentialRequest) returns (DeleteOAuthCredentialResponse);

  // Provider Information
  rpc ListOAuthProviders(OAuthProvidersListRequest) returns (OAuthProvidersListResponse);
  rpc GetToolScopes(OAuthToolScopesRequest) returns (OAuthToolScopesResponse);

  // Health Check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);

  // Integration Management (Admin Operations)
  rpc CreateIntegration(CreateIntegrationRequest) returns (CreateIntegrationResponse);
  rpc UpdateIntegration(UpdateIntegrationRequest) returns (UpdateIntegrationResponse);
  rpc DeleteIntegration(DeleteIntegrationRequest) returns (DeleteIntegrationResponse);
  rpc ListIntegrations(ListIntegrationsRequest) returns (ListIntegrationsResponse);
  rpc GetIntegration(GetIntegrationRequest) returns (GetIntegrationResponse);

  // Integration-based OAuth Methods
  rpc InitiateOAuthByIntegration(InitiateOAuthByIntegrationRequest) returns (InitiateOAuthByIntegrationResponse);
  rpc GetOAuthCredentialsByIntegration(GetOAuthCredentialsByIntegrationRequest) returns (GetOAuthCredentialsByIntegrationResponse);
  rpc DeleteOAuthCredentialsByIntegration(DeleteOAuthCredentialsByIntegrationRequest) returns (DeleteOAuthCredentialsByIntegrationResponse);
  rpc ListUserIntegrations(ListUserIntegrationsRequest) returns (ListUserIntegrationsResponse);
  rpc HandleOAuthCallbackNew(OAuthCallbackRequest) returns (OAuthCallbackResponse);
  
  // API Key Management Methods
  rpc StoreAPIKeyCredentials(StoreAPIKeyCredentialsRequest) returns (StoreAPIKeyCredentialsResponse);
  rpc GetAPIKeyCredentials(GetAPIKeyCredentialsRequest) returns (GetAPIKeyCredentialsResponse);
  rpc UpdateAPIKeyCredentials(UpdateAPIKeyCredentialsRequest) returns (UpdateAPIKeyCredentialsResponse);
  rpc DeleteAPIKeyCredentials(DeleteAPIKeyCredentialsRequest) returns (DeleteAPIKeyCredentialsResponse);
  
}

// OAuth Provider enumeration
enum OAuthProvider {
  OAUTH_PROVIDER_UNSPECIFIED = 0;
  OAUTH_PROVIDER_GOOGLE = 1;
  OAUTH_PROVIDER_MICROSOFT = 2;
  OAUTH_PROVIDER_GITHUB = 3;
  OAUTH_PROVIDER_SLACK = 4;
  OAUTH_PROVIDER_CUSTOM = 5;
  OAUTH_PROVIDER_JIRA = 6;
  OAUTH_PROVIDER_ZOHO = 7;
  OAUTH_PROVIDER_RAPID_HRMS = 8;
}

enum ConnectionType {
  CONNECTION_TYPE_UNSPECIFIED = 0;
  CONNECTION_TYPE_API_KEY = 1;
  CONNECTION_TYPE_OAUTH = 2;
}
// OAuth Authorization Request
message OAuthAuthorizeRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
  repeated string scopes = 4;
  string redirect_uri = 5;
}

// OAuth Authorization Response
message OAuthAuthorizeResponse {
  bool success = 1;
  string message = 2;
  string authorization_url = 3;
  string state = 4;
}

// OAuth Callback Request
message OAuthCallbackRequest {
  string code = 1;
  string state = 2;
  string error = 3;
}

// OAuth Callback Response
message OAuthCallbackResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string tool_name = 4;  // Required field
  string provider = 5;
  string redirect_url = 6;  // Optional redirect URL from authorization request
}

// OAuth Credential Request
message OAuthCredentialRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// OAuth Credential Response
message OAuthCredentialResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string tool_name = 4;  // Required field
  string provider = 5;
  string access_token = 6;
  string refresh_token = 7;
  string token_type = 8;
  int32 expires_in = 9;
  string scope = 10;
  repeated string scopes = 11;

  string bot_token = 12;
  string user_token = 13;
  string bot_user_id = 14;
  string user_id_slack = 15;
  string team_id = 16;
  string team_name = 17;
  string user_scope = 18;
}

// Server OAuth Credential Request (for server-to-server access)
message ServerOAuthCredentialRequest {
  string server_auth_key = 1;
  string user_id = 2;
  string tool_name = 3;  // Required field
  OAuthProvider provider = 4;
}

// OAuth Providers List Request
message OAuthProvidersListRequest {
  // Empty request - returns all available providers
}

// OAuth Provider Info
message OAuthProviderInfo {
  OAuthProvider provider = 1;
  string name = 2;
  string display_name = 3;
  repeated string supported_tools = 4;
  bool is_configured = 5;
}

// OAuth Providers List Response
message OAuthProvidersListResponse {
  bool success = 1;
  string message = 2;
  repeated OAuthProviderInfo providers = 3;
}

// Tool Scopes Request
message OAuthToolScopesRequest {
  string tool_name = 1;  // Required field
  OAuthProvider provider = 2;
}

// Tool Scopes Response
message OAuthToolScopesResponse {
  bool success = 1;
  string message = 2;
  string tool_name = 3;  // Required field
  OAuthProvider provider = 4;
  repeated string scopes = 5;
  string description = 6;
}

// Delete Credential Request
message DeleteOAuthCredentialRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// Delete Credential Response
message DeleteOAuthCredentialResponse {
  bool success = 1;
  string message = 2;
}

// OAuth Refresh Request
message OAuthRefreshRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// OAuth Refresh Response
message OAuthRefreshResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string token_type = 4;
  int32 expires_in = 5;
  string scope = 6;
}

// Health Check Request
message HealthCheckRequest {
  // Empty request
}

// Health Check Response
message HealthCheckResponse {
  bool healthy = 1;
  string status = 2;
  string version = 3;
  map<string, string> dependencies = 4;
}


// API Key Field Definition
message APIKeyFieldDefinition {
  string name = 1;
  bool required = 2;
  string description = 3;
}

// Integration Definition Messages
message IntegrationDefinition {
  string id = 1;
  string logo = 2;
  string name = 3;
  string description = 4;
  ConnectionType connection_type = 5;
  string schema_definition = 6; // JSON string
  bool is_active = 7;
  string created_at = 8;
  string updated_at = 9;
  
  // New structured field for API key integrations
  repeated APIKeyFieldDefinition api_key_fields = 10; // For API key integrations only
}

// Create Integration Request
message CreateIntegrationRequest {
  string admin_user_id = 1; // Admin user ID for audit trail
  string logo = 2;
  string name = 3;
  string description = 4;
  ConnectionType connection_type = 5;
  string schema_definition = 6; // JSON string
}

// Create Integration Response
message CreateIntegrationResponse {
  bool success = 1;
  string message = 2;
  IntegrationDefinition integration = 3;
}

// Update Integration Request
message UpdateIntegrationRequest {
  string admin_user_id = 1; // Admin user ID for audit trail
  string integration_id = 2;
  string logo = 3;
  string name = 4;
  string description = 5;
  ConnectionType connection_type = 6;
  string schema_definition = 7; // JSON string
  bool is_active = 8;
}

// Update Integration Response
message UpdateIntegrationResponse {
  bool success = 1;
  string message = 2;
  IntegrationDefinition integration = 3;
}

// Delete Integration Request
message DeleteIntegrationRequest {
  string admin_user_id = 1; // Admin user ID for audit trail
  string integration_id = 2;
}

// Delete Integration Response
message DeleteIntegrationResponse {
  bool success = 1;
  string message = 2;
}

// List Integrations Request
message ListIntegrationsRequest {
  int32 page = 1; // Page number (default: 1)
  int32 page_size = 2; // Items per page (default: 10, max: 100)
  string provider = 3; // Optional filter by provider
  string integration_type = 4; // Optional filter by integration type
  bool is_enabled = 5; // Optional filter by enabled status
  bool include_inactive = 6; // Include inactive integrations (default: false)
  ConnectionType connection_type_filter = 7; // Optional filter by connection type
}

// List Integrations Response
message ListIntegrationsResponse {
  bool success = 1;
  string message = 2;
  repeated IntegrationDefinition integrations = 3;
  int32 total = 4; // Total number of integrations
  int32 page = 5; // Current page number
  int32 page_size = 6; // Items per page
}

// Get Integration Request
message GetIntegrationRequest {
  string integration_id = 1;
}

// Get Integration Response
message GetIntegrationResponse {
  bool success = 1;
  string message = 2;
  IntegrationDefinition integration = 3;
}

// User Integration Status
message UserIntegrationStatus {
  string user_id = 1;
  string integration_id = 2;
  string integration_name = 3;
  bool is_connected = 4;
  string last_used_at = 5;
  string created_at = 6;
  repeated string scopes = 7;
  ConnectionType connection_type = 8;
  string schema_definition = 9; // JSON string of the integration schema definition (for API key integrations)
}

// List User Integrations Request
message ListUserIntegrationsRequest {
  string user_id = 1;
  ConnectionType connection_type_filter = 2; // Optional filter
}

// List User Integrations Response
message ListUserIntegrationsResponse {
  bool success = 1;
  string message = 2;
  repeated UserIntegrationStatus integrations = 3;
}

// Integration-based OAuth Authorization Request
message InitiateOAuthByIntegrationRequest {
  string user_id = 1;
  string integration_id = 2;
  string redirect_uri = 3;
}

// Integration-based OAuth Authorization Response
message InitiateOAuthByIntegrationResponse {
  bool success = 1;
  string message = 2;
  string authorization_url = 3;
  string state = 4;
}

// Integration-based OAuth Credential Request
message GetOAuthCredentialsByIntegrationRequest {
  string user_id = 1;
  string integration_id = 2;
}

// Integration-based OAuth Credential Response
message GetOAuthCredentialsByIntegrationResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string integration_id = 4;
  string access_token = 5;
  string refresh_token = 6;
  string token_type = 7;
  int32 expires_in = 8;
  string scope = 9;
  UserIntegrationStatus user_integration_status = 10;
}

// Integration-based OAuth Delete Request
message DeleteOAuthCredentialsByIntegrationRequest {
  string user_id = 1;
  string integration_id = 2;
}

// Integration-based OAuth Delete Response
message DeleteOAuthCredentialsByIntegrationResponse {
  bool success = 1;
  string message = 2;
}

// API Key Management Messages

// Store API Key Credentials Request
message StoreAPIKeyCredentialsRequest {
  string user_id = 1;
  string integration_id = 2;
  string credentials = 3; // JSON string of key-value pairs
}

// Store API Key Credentials Response
message StoreAPIKeyCredentialsResponse {
  bool success = 1;
  string message = 2;
  string integration_id = 3;
}

// Get API Key Credentials Request
message GetAPIKeyCredentialsRequest {
  string user_id = 1;
  string integration_id = 2;
}

// Get API Key Credentials Response
message GetAPIKeyCredentialsResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string integration_id = 4;
  string credentials = 5; // JSON string of key-value pairs
  bool is_connected = 6;
  string last_used_at = 7;
  string schema_definition = 8; // JSON string of the integration schema definition
}

// Update API Key Credentials Request
message UpdateAPIKeyCredentialsRequest {
  string user_id = 1;
  string integration_id = 2;
  string credentials = 3; // JSON string of key-value pairs
}

// Update API Key Credentials Response
message UpdateAPIKeyCredentialsResponse {
  bool success = 1;
  string message = 2;
  string integration_id = 3;
}

// Delete API Key Credentials Request
message DeleteAPIKeyCredentialsRequest {
  string user_id = 1;
  string integration_id = 2;
}

// Delete API Key Credentials Response
message DeleteAPIKeyCredentialsResponse {
  bool success = 1;
  string message = 2;
}