from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    CredentialCreate,
    CredentialResponse,
    CredentialListResponse,
    CredentialDetailsResponse,
    CredentialDeleteResponse,
    CredentialInfo,
    CredentialUpdate,
    CredentialUpdateResponse,
    # Import unified variable schemas
    VariableType,
    VariableCreate,
    VariableUpdate,
    VariableResponse,
    VariableListResponse,
    VariableDetailsResponse,
    VariableUpdateResponse,
    VariableDeleteResponse,
    VariableInfo,
)
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict
import logging

credential_router = APIRouter(prefix="/credentials", tags=["credentials"])
user_service = UserServiceClient()


@credential_router.post("", response_model=CredentialResponse)
async def create_credential(
    credential_data: CredentialCreate,
    current_user: dict = Depends(role_required(["user"])),
    type: str = Query("credential", description="Type of variable: 'credential' or 'global-variable'")
):
    """Create a credential or global variable based on type parameter"""
    try:
        # Use unified variable service for both types
        response = await user_service.create_variable(
            owner_id=current_user["user_id"],
            key_name=credential_data.key_name,
            value=credential_data.value,
            description=credential_data.description,
            type=type
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return CredentialResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in create_credential: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.get("", response_model=CredentialListResponse)
async def list_credentials(
    current_user: dict = Depends(role_required(["user"])),
    type: str = Query("credential", description="Type of variable: 'credential' or 'global-variable'")
):
    """List credentials or global variables based on type parameter"""
    try:
        logging.info(f"Listing {type}s for user: {current_user['user_id']}")

        # Use unified variable service for both types
        response = await user_service.list_variables(
            owner_id=current_user["user_id"],
            type=type
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        logging.info(f"Variables response: {response}")
        print(f"DEBUG API: Response success: {response.success}")
        print(f"DEBUG API: Response message: {response.message}")
        print(f"DEBUG API: Variables count: {len(response.variables)}")

        # Convert gRPC response to Pydantic models
        credential_list = []
        for i, var in enumerate(response.variables):
            print(f"DEBUG API: Processing variable {i+1}: {var.key_name}")
            try:
                # Convert protobuf message to dict
                print(f"DEBUG API: Converting protobuf to dict for {var.key_name}")
                var_dict = MessageToDict(var, preserving_proto_field_name=True)
                print(f"DEBUG API: Converted dict: {var_dict}")

                # Create Pydantic model from dict (maintaining backward compatibility)
                print(f"DEBUG API: Creating CredentialInfo for {var.key_name}")
                credential_info = CredentialInfo(
                    id=var_dict.get("id"),
                    key_name=var_dict.get("key_name"),
                    description=var_dict.get("description"),
                    value=var_dict.get("value"),  # Will be empty for credentials, actual value for global variables
                    created_at=var_dict.get("created_at"),
                    updated_at=var_dict.get("updated_at"),
                    last_used_at=var_dict.get("last_used_at"),
                )
                print(f"DEBUG API: CredentialInfo created successfully for {var.key_name}")
                credential_list.append(credential_info)
                print(f"DEBUG API: Added to list, total: {len(credential_list)}")
            except Exception as e:
                print(f"DEBUG API: Error processing variable {var.key_name}: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

        return CredentialListResponse(
            success=response.success, message=response.message, credentials=credential_list
        )
    except Exception as e:
        logging.error(f"Error in list_credentials: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.get("/{credential_id}", response_model=CredentialDetailsResponse)
async def get_credential(
    credential_id: str,
    current_user: dict = Depends(role_required(["user"])),
    type: str = Query("credential", description="Type of variable: 'credential' or 'global-variable'")
):
    """Get a credential or global variable by ID based on type parameter"""
    try:
        logging.info(f"Getting {type} with ID: {credential_id}")

        # Use unified variable service for both types
        response = await user_service.get_variable(
            variable_id=credential_id,
            owner_id=current_user["user_id"],
            type=type
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        logging.info(f"Variable response: {response}")

        # Convert gRPC response to Pydantic model
        credential = None
        if response.variable:
            var_dict = MessageToDict(response.variable, preserving_proto_field_name=True)

            credential = CredentialInfo(
                id=var_dict.get("id"),
                key_name=var_dict.get("key_name"),
                description=var_dict.get("description"),
                value=var_dict.get("value"),  # Will be empty for credentials, actual value for global variables
                created_at=var_dict.get("created_at"),
                updated_at=var_dict.get("updated_at"),
                last_used_at=var_dict.get("last_used_at"),
            )

        return CredentialDetailsResponse(
            success=response.success, message=response.message, credential=credential
        )
    except Exception as e:
        logging.error(f"Error in get_credential: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.delete("/{credential_id}", response_model=CredentialDeleteResponse)
async def delete_credential(
    credential_id: str,
    current_user: dict = Depends(role_required(["user"])),
    type: str = Query("credential", description="Type of variable: 'credential' or 'global-variable'")
):
    """Delete a credential or global variable by ID based on type parameter"""
    try:
        # Use unified variable service for both types
        response = await user_service.delete_variable(
            variable_id=credential_id,
            owner_id=current_user["user_id"],
            type=type
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        return CredentialDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.put("/{credential_id}", response_model=CredentialResponse)
async def update_credential(
    credential_id: str,
    credential_data: CredentialUpdate,
    current_user: dict = Depends(role_required(["user"])),
    type: str = Query("credential", description="Type of variable: 'credential' or 'global-variable'")
):
    """Update a credential or global variable by ID based on type parameter"""
    try:
        # Use unified variable service for both types
        response = await user_service.update_variable(
            variable_id=credential_id,
            owner_id=current_user["user_id"],
            type=type,
            key_name=credential_data.key_name,
            value=credential_data.value,
            description=credential_data.description,
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        logging.info(f"Updated variable response: {response}")
        return CredentialResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name,
        )
    except Exception as e:
        logging.error(f"Error in update_credential: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
