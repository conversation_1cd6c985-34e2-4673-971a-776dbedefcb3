from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    VariableType,
    VariableCreate,
    VariableUpdate,
    VariableResponse,
    VariableListResponse,
    VariableDetailsResponse,
    VariableUpdateResponse,
    VariableDeleteResponse,
    VariableInfo,
)
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict
import logging

variable_router = APIRouter(prefix="/variables", tags=["variables"])
user_service = UserServiceClient()


@variable_router.post("", response_model=VariableResponse)
async def create_variable(
    variable_data: VariableCreate,
    type: VariableType = Query(..., description="Type of variable to create"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Create a new variable (credential or global variable)"""
    try:
        response = await user_service.create_variable(
            owner_id=current_user["user_id"],
            key_name=variable_data.key_name,
            value=variable_data.value,
            description=variable_data.description,
            type=type.value
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return VariableResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in create_variable: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@variable_router.get("", response_model=VariableListResponse)
async def list_variables(
    type: VariableType = Query(..., description="Type of variables to list"),
    current_user: dict = Depends(role_required(["user"]))
):
    """List variables by type (credentials or global variables)"""
    try:
        logging.info(f"Listing variables of type {type.value} for user: {current_user['user_id']}")
        response = await user_service.list_variables(
            owner_id=current_user["user_id"],
            type=type.value
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        # Convert gRPC response to API response format
        variables = []
        for var in response.variables:
            variable_info = VariableInfo(
                id=var.id,
                key_name=var.key_name,
                description=var.description,
                type=VariableType(var.type),
                value=var.value if var.value else None,  # Handle empty strings
                has_value=var.has_value,
                created_at=var.created_at,
                updated_at=var.updated_at,
                last_used_at=var.last_used_at
            )
            variables.append(variable_info)

        return VariableListResponse(
            success=response.success,
            message=response.message,
            variables=variables
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in list_variables: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@variable_router.get("/{variable_id}", response_model=VariableDetailsResponse)
async def get_variable(
    variable_id: str,
    type: VariableType = Query(..., description="Type of variable to get"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Get a specific variable by ID and type"""
    try:
        response = await user_service.get_variable(
            variable_id=variable_id,
            owner_id=current_user["user_id"],
            type=type.value
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        variable_info = VariableInfo(
            id=response.variable.id,
            key_name=response.variable.key_name,
            description=response.variable.description,
            type=VariableType(response.variable.type),
            value=response.variable.value if response.variable.value else None,
            has_value=response.variable.has_value,
            created_at=response.variable.created_at,
            updated_at=response.variable.updated_at,
            last_used_at=response.variable.last_used_at
        )

        return VariableDetailsResponse(
            success=response.success,
            message=response.message,
            variable=variable_info
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in get_variable: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@variable_router.put("/{variable_id}", response_model=VariableUpdateResponse)
async def update_variable(
    variable_id: str,
    variable_data: VariableUpdate,
    type: VariableType = Query(..., description="Type of variable to update"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Update a variable by ID and type"""
    try:
        response = await user_service.update_variable(
            variable_id=variable_id,
            owner_id=current_user["user_id"],
            type=type.value,
            key_name=variable_data.key_name,
            value=variable_data.value,
            description=variable_data.description
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return VariableUpdateResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in update_variable: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@variable_router.delete("/{variable_id}", response_model=VariableDeleteResponse)
async def delete_variable(
    variable_id: str,
    type: VariableType = Query(..., description="Type of variable to delete"),
    current_user: dict = Depends(role_required(["user"]))
):
    """Delete a variable by ID and type"""
    try:
        response = await user_service.delete_variable(
            variable_id=variable_id,
            owner_id=current_user["user_id"],
            type=type.value
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        return VariableDeleteResponse(
            success=response.success,
            message=response.message
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in delete_variable: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
