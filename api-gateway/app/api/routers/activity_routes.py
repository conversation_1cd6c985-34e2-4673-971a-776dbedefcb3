"""
Activity Routes

This module provides FastAPI routes for activity management including:
- Activity creation
- Activity log creation
- Activity event creation
- Activity retrieval and listing
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from app.services.activity_service import activity_service
from app.schemas.activity import (
    CreateActivityRequestSchema,
    CreateActivityResponseSchema,
    CreateActivityLogRequestSchema,
    CreateActivityEventRequestSchema,
    CreateActivityEventResponseSchema,
    GetActivityResponseSchema,
    ListActivitiesResponseSchema,
    ListActivityLogsRequestSchema,
    ListActivityLogsResponseSchema,
    ListActivityEventsRequestSchema,
    ListActivityEventsResponseSchema,
    ActivitySchema,
    ActivityLogSchema,
    ActivityEventSchema,
    ActivityType,
    LogType,
    LogStatus,
    UpdateActivityRequestSchema,
    UpdateActivityResponseSchema,
    DeleteActivityResponseSchema,
)
from app.core.auth_guard import role_required
from app.core.security import validate_server_auth_key
from app.utils.parse_error import parse_error
import logging

logger = logging.getLogger(__name__)

activity_router = APIRouter(prefix="/activities", tags=["activities"])


@activity_router.post("", response_model=CreateActivityResponseSchema, status_code=201, dependencies=[Depends(validate_server_auth_key)])
async def create_activity(
    request_data: CreateActivityRequestSchema = Body(...),
):
    """
    Create a new activity.
    """
    try:
        activity_data = request_data.dict()
        result = await activity_service.create_activity(activity_data)
        
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create activity"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.post("/logs", response_model=ActivityLogSchema, status_code=201, dependencies=[Depends(validate_server_auth_key)])
async def create_activity_log(
    request_data: CreateActivityLogRequestSchema = Body(...),
):
    """
    Create a new activity log.
    """
    try:
        log_data = request_data.dict()
        result = await activity_service.create_activity_log(log_data)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating activity log: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.post("/events", response_model=CreateActivityEventResponseSchema, status_code=201, dependencies=[Depends(validate_server_auth_key)])
async def create_activity_event(
    request_data: CreateActivityEventRequestSchema = Body(...),
):
    """
    Create a new activity event. This will automatically create the parent activity.
    """
    try:
        event_data = request_data.dict()
        result = await activity_service.create_activity_event(event_data)
        
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create activity event"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating activity event: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.put("/{activity_id}", response_model=UpdateActivityResponseSchema, dependencies=[Depends(validate_server_auth_key)])
async def update_activity(
    activity_id: str,
    request_data: UpdateActivityRequestSchema = Body(...),
):
    """
    Update an existing activity.
    """
    try:
        activity_data_dict = request_data.dict(exclude_unset=True)
        user_id = activity_data_dict.pop("user_id")
        result = await activity_service.update_activity(
            activity_id=activity_id,
            user_id=user_id,
            activity_data=activity_data_dict,
        )
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to update activity"))
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.delete("/{activity_id}", response_model=DeleteActivityResponseSchema)
async def delete_activity(
    activity_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Delete an activity and its associated logs and events.
    """
    try:
        result = await activity_service.delete_activity(
            activity_id=activity_id,
            user_id=current_user["user_id"],
        )
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to delete activity"))
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.get("/{correlation_id}", response_model=GetActivityResponseSchema)
async def get_activity(
    correlation_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get a specific activity with its logs and events by correlation_id.
    """
    try:
        result = await activity_service.get_activity(correlation_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting activity: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.get("", response_model=ListActivitiesResponseSchema)
async def list_activities(
    type: Optional[ActivityType] = Query(None, description="Filter by activity type"),
    resource_id: Optional[str] = Query(None, description="Filter by resource ID"),
    page_size: int = Query(10, ge=1, le=100, description="Number of activities per page"),
    page_token: Optional[str] = Query(None, description="Page number for pagination (defaults to 1)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List activities for the authenticated user with optional filtering. Each activity includes its related logs and events.
    """
    try:
        # Use authenticated user's ID for filtering
        result = await activity_service.list_activities(
            user_id=current_user["user_id"],
            type=type,
            resource_id=resource_id,
            page_size=page_size,
            page_token=page_token,
        )
        
        # Check if the service returned an error response
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to list activities"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing activities: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.get("/logs/list", response_model=ListActivityLogsResponseSchema)
async def list_activity_logs(
    activity_id: Optional[str] = Query(None, description="Filter by activity ID"),
    log_type: Optional[LogType] = Query(None, description="Filter by log type"),
    log_status: Optional[LogStatus] = Query(None, description="Filter by log status"),
    page_size: int = Query(10, ge=1, le=100, description="Number of logs per page"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List activity logs for the authenticated user with optional filtering and pagination.
    """
    try:
        # Use authenticated user's ID for filtering
        result = await activity_service.list_activity_logs(
            activity_id=activity_id,
            user_id=current_user["user_id"],
            log_type=log_type if log_type else None,
            log_status=log_status if log_status else None,
            page_size=page_size,
            offset=offset,
        )
        
        # Check if the service returned an error response
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to list activity logs"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing activity logs: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
    

@activity_router.get("/logs/list/{correlation_id}", response_model=ListActivityLogsResponseSchema)
async def list_activity_logs_by_correlation_id(
    correlation_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List activity logs for a given correlation_id.
    """
    try:
        result = await activity_service.list_activity_logs_by_correlation_id(correlation_id)
        
        if isinstance(result, dict) and not result.get("success", True):
            if "not found" in result.get("message", "").lower():
                raise HTTPException(status_code=404, detail=result.get("message"))
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to list activity logs"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing activity logs by correlation_id: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@activity_router.get("/events/list", response_model=ListActivityEventsResponseSchema)
async def list_activity_events(
    activity_id: Optional[str] = Query(None, description="Filter by activity ID"),
    event_name: Optional[str] = Query(None, description="Filter by event name"),
    page_size: int = Query(10, ge=1, le=100, description="Number of events per page"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List activity events for the authenticated user with optional filtering and pagination.
    """
    try:
        # Use authenticated user's ID for filtering
        result = await activity_service.list_activity_events(
            activity_id=activity_id,
            user_id=current_user["user_id"],
            event_name=event_name,
            page_size=page_size,
            offset=offset,
        )
        
        # Check if the service returned an error response
        if isinstance(result, dict) and not result.get("success", True):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to list activity events"))
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing activity events: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
