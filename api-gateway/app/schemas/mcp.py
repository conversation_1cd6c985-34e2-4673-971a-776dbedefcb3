from pydantic import BaseModel, <PERSON>, validator
from typing import Dict, Any, Optional, List
import json
from enum import Enum


class McpCategory(str, Enum):
    GENERAL = "general"
    SALES = "sales"
    MARKETING = "marketing"
    ENGINEERING = "engineering"
    FINANCE = "finance"
    HR = "hr"

class McpComponentCategory(str, Enum):
    NOTIFICATIONS_ALERTS = "notifications_alerts"
    COMMUNICATION = "communication"
    SOCIAL_MEDIA = "social_media"
    DATABASE = "database"
    CLOUD_STORAGE = "cloud_storage"
    DEVOPS_SYSTEM = "devops_system"
    FILE_HANDLING = "file_handling"

class UrlTypeEnum(str, Enum):
    SSE = "sse"
    STREAMABLE_HTTP = "streamable-http"
    STDIO = "stdio"


class VisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"


class Owner(BaseModel):
    id: str
    name: Optional[str] = None
    email: Optional[str] = None


class MCPUrl(BaseModel):
    url: str
    type: UrlTypeEnum


class EnvKey(BaseModel):
    key: str
    description: str

class OAuthDetails(BaseModel):
    provider: str
    tool_name: str
    
class MCPCreate(BaseModel):
    logo: Optional[str] = None
    name: str
    description: Optional[str] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    config: Optional[List[MCPUrl]] = None
    github_access_token: Optional[str] = None
    category: McpCategory
    visibility: VisibilityEnum
    tags: Optional[List[str]] = None
    status: StatusEnum
    user_ids: Optional[List[str]] = None
    env_keys: Optional[List[EnvKey]] = None
    mcp_type: Optional[UrlTypeEnum] = None
    component_category: Optional[McpComponentCategory] = None
    oauth_details: Optional[OAuthDetails] = None


class CreateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class OwnerTypeEnum(str, Enum):
    USER = "user"
    ORGANIZATION = "organization"
    PLATFORM = "platform"


class MCPPatchPayload(BaseModel):
    name: Optional[str] = Field(None, min_length=1)
    logo: Optional[str] = None
    description: Optional[str] = None
    visibility: Optional[VisibilityEnum] = None
    user_ids: Optional[List[str]] = None
    category: Optional[McpCategory] = None
    tags: Optional[List[str]] = None
    status: Optional[StatusEnum] = None
    config: Optional[List[MCPUrl]] = None
    github_access_token: Optional[str] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    env_keys: Optional[List[EnvKey]] = None
    mcp_type: Optional[UrlTypeEnum] = None
    component_category: Optional[McpComponentCategory] = None
    oauth_details: Optional[OAuthDetails] = None

    class Config:
        use_enum_values = True


class DeploymentStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"


class EnvCredentialStatus(str, Enum):
    PENDING_INPUT = "pending_input"
    PROVIDED = "provided"
    NOT_REQUIRED = "not_required"

class MCPInDB(BaseModel):
    id: str
    name: str
    logo: Optional[str] = None
    description: Optional[str] = None
    owner_id: str
    user_ids: Optional[List[str]] = None
    owner_type: str
    config: Optional[List[Dict[str, str]]] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    deployment_status: Optional[DeploymentStatus] = None
    visibility: str
    tags: Optional[List[str]] = None
    status: str
    created_at: str
    updated_at: str
    image_name: Optional[str] = None
    category: Optional[str] = None
    mcp_tools_config: Optional[dict] = None
    is_added: Optional[bool] = False
    is_quick_tool: Optional[bool] = False
    env_keys: Optional[List[EnvKey]] = None
    component_category: Optional[McpComponentCategory] = None
    env_credential_status:Optional[EnvCredentialStatus] = None
    oauth_details: Optional[OAuthDetails] = None
    
    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    @validator("config", pre=True)
    def validate_urls(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    @validator("env_keys", pre=True)
    def validate_env_keys(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    class Config:
        from_attributes = True


class MCPResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None


class CreateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class UpdateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None


class DeleteMCPResponse(BaseModel):
    success: bool
    message: str


class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


class PaginatedMCPResponse(BaseModel):
    data: List[MCPInDB]
    metadata: PaginationMetadata


class McpTemplateBase(BaseModel):
    name: str
    description: str
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    git_token: Optional[str] = None
    gcr_image: Optional[str] = None
    sse_url: Optional[str] = None
    tags: Optional[Dict[str, Any]] = None


class McpTemplateCreate(McpTemplateBase):
    visibility: VisibilityEnum
    status: StatusEnum
    category: McpCategory


class McpTemplateUpdate(McpTemplateBase):
    visibility: VisibilityEnum
    status: StatusEnum
    category: McpCategory


class McpTemplateInDB(BaseModel):
    id: str
    name: str
    description: str
    visibility: str
    category: str
    tags: Optional[Dict[str, Any]] = None
    status: str
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    gcr_image: Optional[str] = None
    sse_url: Optional[str] = None
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class McpTemplateResponse(BaseModel):
    success: bool
    message: str
    template: McpTemplateInDB


class CreateMcpTemplateResponse(BaseModel):
    success: bool
    message: str
    template_id: Optional[str] = None


class UpdateMcpTemplateResponse(BaseModel):
    success: bool
    message: str


class DeleteMcpTemplateResponse(BaseModel):
    success: bool
    message: str


class ListMcpTemplatesResponse(BaseModel):
    templates: List[McpTemplateInDB]
    total: int
    page: int
    total_pages: int


class CreateMcpFromTemplateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    owner_type: OwnerTypeEnum

    class Config:
        use_enum_values = True


class CreateMcpFromTemplateResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class MCPsByIdsRequest(BaseModel):
    ids: List[str]


class MCPsByIdsResponse(BaseModel):
    success: bool
    message: str
    mcps: List[MCPInDB]
    total: int


class MCPUrlsRequest(BaseModel):
    ids: List[str]


class MCPUrlItem(BaseModel):
    mcp_id: str
    url: Optional[str] = None


class MCPUrlsResponse(BaseModel):
    success: bool
    message: str
    config: List[MCPUrlItem]


class DeploymentUpdatePayload(BaseModel):
    mcp_id: str
    deployment_status: DeploymentStatus
    type: Optional[UrlTypeEnum] = None
    image_name: Optional[str] = None
    error_message: Optional[str] = None
    url: Optional[str] = None

    class Config:
        use_enum_values = True


class DeploymentUpdateResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None


class EnvKeyValue(BaseModel):
    key: str
    value: str


# Schemas for Container Management
class CreateContainerRequest(BaseModel):
    mcp_id: str
    user_id: str
    type: str = "stdio"
    env: Optional[List[EnvKeyValue]] = None


class CreateContainerResponse(BaseModel):
    success: bool
    message: str
    container_id: Optional[str] = None
    # Add any other relevant fields from the mcp-service response, e.g., status


class StopContainerResponse(BaseModel):
    success: bool
    message: str


class GetContainerStatusResponse(BaseModel):
    success: bool
    message: str
    container_id: str
    status: str  # e.g., "running", "stopped", "error"
    details: Optional[Dict[str, Any]] = None  # For any extra details like ports, logs snippet etc.


class DeleteContainerResponse(BaseModel):
    success: bool
    message: str


class ToolOutputSchemaUpdatePayload(BaseModel):
    mcp_id: str
    tool_name: str
    output_schema_json: Dict


class ToggleMcpVisibilityResponseAPI(BaseModel):
    success: bool
    message: str


class UpdateMcpEnvVarsRequestApi(BaseModel):
    env_key_values: List[EnvKeyValue] = Field(
        ..., description="List of environment key-value pairs."
    )


class UpdateMcpEnvVarsResponseApi(BaseModel):
    success: bool
    message: str
    user_mcp_assignment_id: Optional[str] = None
    env_credential_status: Optional[str] = None


class McpEnvKeyDefinitionApi(BaseModel):
    key: str
    description: Optional[str] = None


class GetMcpEnvVarsResponseApi(BaseModel):
    success: bool
    message: str
    env_key_values: Optional[List[EnvKeyValue]] = None
    defined_env_keys: Optional[List[McpEnvKeyDefinitionApi]] = None
    env_credential_status: Optional[str] = None
    user_mcp_assignment_id: Optional[str] = None
