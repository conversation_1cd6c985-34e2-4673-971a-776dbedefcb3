from typing import Dict, Any


def extract_user_details_from_jwt(current_user: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract user details from JWT token and transform them to match the format
    expected by the _create_owner_proto function in agent_service.py.

    This function maps JWT token fields to the format expected by the gRPC Owner proto:
    - user_id -> id
    - email -> email
    - name -> full_name
    - fcm_token -> fcm_token (optional, defaults to None)

    Args:
        current_user (Dict[str, Any]): The current user dict from JWT token containing:
            - user_id: str
            - email: str
            - role: str
            - name: str
            - organisation_id: str

    Returns:
        Dict[str, Any]: User details in the format expected by _create_owner_proto:
            - id: str
            - email: str
            - full_name: str
            - fcm_token: Optional[str]

    Example:
        >>> jwt_user = {
        ...     "user_id": "123",
        ...     "email": "<EMAIL>",
        ...     "role": "admin",
        ...     "name": "<PERSON>",
        ...     "organisation_id": "org123"
        ... }
        >>> extract_user_details_from_jwt(jwt_user)
        {
            "id": "123",
            "email": "<EMAIL>",
            "full_name": "<PERSON>e",
            "fcm_token": None
        }
    """
    return {
        "id": current_user.get("user_id"),
        "email": current_user.get("email"),
        "full_name": current_user.get("name"),
        "fcm_token": current_user.get("fcm_token")  # This will be None if not present
    }