from fastapi import HTTPException
import grpc
from typing import Optional, Dict, Any, List

from pydantic import BaseModel
from app.core.config import settings
from app.grpc_ import mcp_pb2, mcp_pb2_grpc
import json
from google.protobuf.field_mask_pb2 import FieldMask


from app.schemas.mcp import (
    DeploymentStatus,
    EnvKeyValue,
    OAuthDetails,
    StatusEnum,
    UrlTypeEnum,
    VisibilityEnum,
)


class MCPServiceClient:
    """
    Client for interacting with the MCP gRPC service.
    """

    def __init__(self):
        """
        Initializes the MCPServiceClient with a gRPC channel.
        """
        self.channel = grpc.insecure_channel(
            f"{settings.MCP_SERVICE_HOST}:{settings.MCP_SERVICE_PORT}"
        )
        self.stub = mcp_pb2_grpc.MCPServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")

    def _create_owner_proto(self, owner_details: dict) -> mcp_pb2.Owner:
        return mcp_pb2.Owner(
            id=owner_details.get("id", ""),
            email=owner_details.get("email", ""),
            full_name=owner_details.get("full_name", ""),
            fcm_token=owner_details.get("fcm_token", ""),
        )

    def _get_owner_type_enum(self, owner_type: str) -> mcp_pb2.OwnerType:
        return {
            "user": mcp_pb2.OwnerType.USER,
            "enterprise": mcp_pb2.OwnerType.ENTERPRISE,
            "platform": mcp_pb2.OwnerType.PLATFORM,
        }.get(owner_type.lower(), mcp_pb2.OwnerType.USER)

    def _get_visibility_enum(self, visibility: str) -> mcp_pb2.Visibility:
        return {
            "private": mcp_pb2.Visibility.PRIVATE,
            "public": mcp_pb2.Visibility.PUBLIC,
        }.get(visibility.lower(), mcp_pb2.Visibility.PRIVATE)

    def _get_status_enum(self, status: str) -> mcp_pb2.Status:
        return {
            "active": mcp_pb2.Status.ACTIVE,
            "inactive": mcp_pb2.Status.INACTIVE,
        }.get(status.lower(), mcp_pb2.Status.ACTIVE)

    def _get_category_enum(self, category: str) -> mcp_pb2.MCPCategory:
        """Convert category string to enum value."""
        return {
            "general": mcp_pb2.MCPCategory.GENERAL,
            "engineering": mcp_pb2.MCPCategory.ENGINEERING,
            "finance": mcp_pb2.MCPCategory.FINANCE,
            "hr": mcp_pb2.MCPCategory.HR,
            "marketing": mcp_pb2.MCPCategory.MARKETING,
            "sales": mcp_pb2.MCPCategory.SALES,
        }.get(category.lower(), mcp_pb2.MCPCategory.GENERAL)

    def _get_url_type_enum(self, url_type: str) -> mcp_pb2.UrlType:
        """Convert URL type string to enum value."""
        return {
            "sse": mcp_pb2.UrlType.SSE,
            "http": mcp_pb2.UrlType.HTTP,
        }.get(url_type.lower(), mcp_pb2.UrlType.SSE)

    async def createMCP(
        self,
        name: str,
        description: Optional[str],
        owner_details: dict,
        owner_type: str,
        category: str,
        visibility: str,
        status: str,
        git_url: Optional[str] = None,
        git_branch: Optional[str] = None,
        github_access_token: Optional[str] = None,
        config: Optional[List[dict[str, str]]] = None,
        tags: Optional[List[str]] = [],
        user_ids: Optional[List[str]] = None,
        logo: Optional[str] = None,
        env_keys: Optional[List[Dict[str, str]]] = None,
        mcp_type: Optional[str] = None,
        component_category: Optional[str] = None,
        oauth_details: Optional[OAuthDetails] = None,
    ) -> mcp_pb2.MCPResponse:
        """
        Creates a new MCP configuration via gRPC.
        """
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request = mcp_pb2.CreateMCPRequest(
                logo=logo if logo else None,
                name=name,
                description=description,
                owner=owner_proto,
                owner_type=self._get_owner_type_enum(owner_type),
                git_url=git_url,
                visibility=self._get_visibility_enum(visibility),
                category=self._get_category_enum(category),
                component_category=component_category,
                tags=tags,
                github_access_token=github_access_token,
                git_branch=git_branch,
                status=self._get_status_enum(status),
                config=(
                    [
                        mcp_pb2.MCPUrl(
                            url=u.url, type=u.type.value if hasattr(u.type, "value") else u.type
                        )
                        for u in config
                    ]
                    if config
                    else []
                ),
                user_ids=user_ids if user_ids else [],
                env_keys=(
                    [
                        mcp_pb2.EnvKey(**(ek.dict() if isinstance(ek, BaseModel) else ek))
                        for ek in env_keys
                    ]
                    if env_keys
                    else []
                ),
                mcp_type=mcp_type if mcp_type else None,
                oauth_details=(
                    mcp_pb2.OAuthDetails(
                        **(
                            oauth_details.dict()
                            if isinstance(oauth_details, BaseModel)
                            else oauth_details
                        )
                    )
                    if oauth_details
                    else None
                ),
            )
            response = self.stub.createMCP(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR Client] gRPC error in createMCP: {str(e)}")
            raise self._handle_error(e)

    async def getMCPById(self, mcp_id: str, user_id: Optional[str] = None) -> mcp_pb2.MCPResponse:
        """
        Gets an MCP by ID.
        """
        try:
            request = mcp_pb2.GetMCPRequest(id=mcp_id, user_id=user_id)
            response = self.stub.getMCP(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def updateMCP(
        self, mcp_id: str, update_fields: Dict[str, Any], current_owner_details: dict
    ) -> mcp_pb2.MCPResponse:
        try:
            request_args = {"id": mcp_id}
            proto_field_paths = []

            request_args["owner"] = self._create_owner_proto(current_owner_details)

            if "name" in update_fields:
                request_args["name"] = update_fields["name"]
                proto_field_paths.append("name")
            if "logo" in update_fields:
                request_args["logo"] = update_fields["logo"]
                proto_field_paths.append("logo")
            if "description" in update_fields:
                request_args["description"] = update_fields["description"]
                proto_field_paths.append("description")

            if "visibility" in update_fields:
                request_args["visibility"] = self._get_visibility_enum(update_fields["visibility"])
                proto_field_paths.append("visibility")

            if "user_ids" in update_fields:
                request_args["user_ids"] = update_fields["user_ids"]
                proto_field_paths.append("user_ids")

            if "category" in update_fields:
                request_args["category"] = self._get_category_enum(update_fields["category"])
                proto_field_paths.append("category")

            if "tags" in update_fields:
                # Ensure tags is an array
                if isinstance(update_fields["tags"], str):
                    try:
                        parsed_tags = json.loads(update_fields["tags"])
                        if isinstance(parsed_tags, dict):
                            # Convert dict to array of strings
                            request_args["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                        elif isinstance(parsed_tags, list):
                            request_args["tags"] = parsed_tags
                        else:
                            request_args["tags"] = []
                    except json.JSONDecodeError:
                        request_args["tags"] = []
                else:
                    request_args["tags"] = update_fields["tags"]
                proto_field_paths.append("tags")

            if "status" in update_fields:
                request_args["status"] = self._get_status_enum(update_fields["status"])
                proto_field_paths.append("status")

            if "config" in update_fields:
                request_args["config"] = update_fields["config"]
                proto_field_paths.append("config")

            if "git_branch" in update_fields:
                request_args["git_branch"] = update_fields["git_branch"]
                proto_field_paths.append("git_branch")

            if "git_url" in update_fields:
                request_args["git_url"] = update_fields["git_url"]
                proto_field_paths.append("git_url")

            if "github_access_token" in update_fields:
                request_args["github_access_token"] = update_fields["github_access_token"]
                proto_field_paths.append("github_access_token")

            if "env_keys" in update_fields:
                env_keys_list = update_fields["env_keys"]
                request_args["env_keys"] = [
                    mcp_pb2.EnvKey(**(ek.dict() if isinstance(ek, BaseModel) else ek))
                    for ek in env_keys_list or []
                ]
                proto_field_paths.append("env_keys")

            if "component_category" in update_fields:
                request_args["component_category"] = update_fields["component_category"]
                proto_field_paths.append("component_category")

            if "oauth_details" in update_fields:
                oauth_details = update_fields["oauth_details"]
                request_args["oauth_details"] = oauth_details = (
                    mcp_pb2.OAuthDetails(
                        **(
                            oauth_details.dict()
                            if isinstance(oauth_details, BaseModel)
                            else oauth_details
                        )
                    )
                    if oauth_details
                    else None
                )
                proto_field_paths.append("oauth_details")

            if not proto_field_paths:
                raise ValueError("No updatable fields provided.")

            request_args["update_mask"] = FieldMask(paths=proto_field_paths)
            request = mcp_pb2.UpdateMCPRequest(**request_args)
            response = self.stub.updateMCP(request)
            return response

        except grpc.RpcError as e:
            print(f"[ERROR gRPC Client] Exception in updateMCP: {str(e)}")
            raise self._handle_error(e)

    async def deleteMCP(
        self, mcp_id: str, user_id: Optional[str] = None
    ) -> mcp_pb2.DeleteMCPResponse:
        """
        Deletes an MCP by ID.
        """
        try:
            request = mcp_pb2.DeleteMCPRequest(id=mcp_id, user_id=user_id if user_id else None)
            response = self.stub.deleteMCP(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def listMCPS(
        self,
        page: int = 1,
        page_size: int = 10,
        owner_id: Optional[str] = None,
        category: Optional[str] = None,
        visibility: Optional[VisibilityEnum] = None,
        status: Optional[StatusEnum] = None,
        url_type: Optional[UrlTypeEnum] = None,
        tags: Optional[List[str]] = [],
        deployment_status: Optional[DeploymentStatus] = None,
        search: Optional[str] = None,
        component_category: Optional[str] = None,
        quick_tools_only: Optional[bool] = None,
    ) -> mcp_pb2.ListMCPsResponse:
        try:
            request = mcp_pb2.ListMCPsRequest(page=page, page_size=page_size)

            if owner_id:
                request.owner_id = owner_id
            if category:
                category = (category if category else None,)

            if visibility:
                request.visibility = self._get_visibility_enum(visibility)
            if status:
                request.status = self._get_status_enum(status)
            if url_type:
                request.url_type = self._get_url_type_enum(url_type)
            if tags:
                request.tags.extend(tags)
            if deployment_status:
                request.deployment_status = deployment_status.value

            if search:
                request.search = search

            if component_category:
                request.component_category = component_category

            if quick_tools_only is not None:
                request.quick_tools_only = quick_tools_only

            response = self.stub.listMCPs(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in listMCPS: {str(e)}")
            raise self._handle_error(e)

    async def listMCPsMarketplace(
        self,
        page: int = 1,
        page_size: int = 10,
        user_id: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = [],
        component_category: Optional[str] = None,
    ) -> mcp_pb2.ListMCPsResponse:
        try:
            request = mcp_pb2.ListMCPsMarketplaceRequest(page=page, page_size=page_size)

            if user_id:
                request.user_id = user_id
            if category:
                request.category = self._get_category_enum(category)
            if tags:
                request.tags = tags

            if component_category:
                request.component_category = component_category
            response = self.stub.listMCPsMarketplace(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in listMCPS: {str(e)}")
            raise self._handle_error(e)

    async def getMCPsByIds(self, mcp_ids: List[str]) -> mcp_pb2.ListMCPsResponse:
        """
        Gets multiple MCPs by their IDs.
        """
        try:
            request = mcp_pb2.GetMCPsByIdsRequest(ids=mcp_ids)
            response = self.stub.getMCPsByIds(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def getMCPByIdMarketplace(
        self, mcp_id: str, user_id: Optional[str] = None
    ) -> mcp_pb2.MCPResponse:
        """
        Gets an MCP by ID for marketplace display.

        Args:
            mcp_id: The unique identifier of the MCP to retrieve
            user_id: Optional user ID for personalized results

        Returns:
            Response containing the detailed MCP information
        """
        try:
            request_args = {"id": mcp_id}
            if user_id:
                request_args["user_id"] = user_id

            request = mcp_pb2.GetMCPRequest(**request_args)
            response = self.stub.getMarketplaceMCPDetail(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_marketplace_mcps(
        self,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = [],
        sort_by: Optional[str] = None,
        component_category: Optional[str] = None,
    ) -> Any:
        """
        Retrieves a paginated list of public MCP servers for the marketplace.

        Args:
            page: Page number for pagination
            page_size: Number of items per page
            search: Optional search term to filter by name or description
            category: Optional category filter
            tags: Optional tags filter as a dictionary of key-value pairs
            sort_by: Optional sort criteria (NEWEST, OLDEST, MOST_POPULAR, HIGHEST_RATED)

        Returns:
            Response containing the list of marketplace MCPs and pagination metadata
        """
        try:
            request_args = {
                "page": page,
                "page_size": page_size,
                "visibility": "PUBLIC",  # Marketplace only shows public MCPs
            }

            if search:
                request_args["search"] = search
            if category:
                request_args["category"] = category
            if tags:
                request_args["tags"] = tags
            if sort_by:
                request_args["sort_by"] = sort_by

            if component_category:
                request_args["component_category"] = component_category

            request = mcp_pb2.GetMarketplaceMCPsRequest(**request_args)
            response = self.stub.getMarketplaceMCPs(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_marketplace_mcps: {e.details()}")
            raise self._handle_error(e)

    async def rate_mcp(self, mcp_id: str, user_id: str, rating: float) -> mcp_pb2.RateMCPResponse:
        """
        Rate an MCP server with a score from 1 to 5.

        Args:
            mcp_id: The ID of the MCP server to rate
            user_id: The ID of the user providing the rating
            rating: Rating value between 1.0 and 5.0

        Returns:
            The gRPC RateMCPResponse
        """
        try:
            request = mcp_pb2.RateMCPRequest(mcp_id=mcp_id, user_id=user_id, rating=rating)
            response = self.stub.rateMCP(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in rate_mcp: {str(e)}")
            raise self._handle_error(e)

    async def use_mcp(self, mcp_id: str, user_id: str) -> mcp_pb2.UseMCPResponse:
        """
        Mark an MCP server as used and increment its usage count.

        Args:
            mcp_id: The ID of the MCP server to use
            user_id: The ID of the user using the MCP server

        Returns:
            The gRPC UseMCPResponse
        """
        try:
            request = mcp_pb2.UseMCPRequest(mcp_id=mcp_id, user_id=user_id)
            response = self.stub.useMCP(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in use_mcp: {str(e)}")
            raise self._handle_error(e)

    async def update_deployment_status(
        self,
        mcp_id: str,
        deployment_status: str,
        type: Optional[str] = None,
        image_name: Optional[str] = None,
        error_message: Optional[str] = None,
        url: Optional[str] = None,
    ) -> mcp_pb2.MCPResponse:
        """
        Updates the deployment status of an MCP.

        Args:
            mcp_id: The ID of the MCP to update
            deployment_status: The new deployment status (pending/completed)
            type: Optional deployment type (sse, http, stdio)
            image_name: Optional image name for the deployment
            error_message: Optional error message if deployment failed
            url: Optional URL for the deployed service

        Returns:
            MCPResponse with success status, message, and updated MCP
        """
        try:
            request_args = {
                "id": mcp_id,
                "deployment_status": deployment_status,
            }

            # Add optional fields if provided
            if type:
                request_args["type"] = type
            if image_name:
                request_args["image_name"] = image_name
            if error_message:
                request_args["error_message"] = error_message
            if url:
                request_args["url"] = url

            request = mcp_pb2.UpdateDeploymentStatusRequest(**request_args)
            response = self.stub.UpdateDeploymentStatus(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in update_deployment_status: {str(e)}")
            raise self._handle_error(e)

    async def create_container(
        self, mcp_id: str, user_id: str, type: str, env: Optional[List[EnvKeyValue]] = None
    ) -> mcp_pb2.CreateContainerResponse:
        """
        Sends a request to MCP service to create a container.
        """
        try:
            env_proto = []
            if env:  # env is now a list of EnvKeyValue objects
                env_proto = [
                    mcp_pb2.EnvVar(key=kv_pair.key, value=kv_pair.value) for kv_pair in env
                ]

            request = mcp_pb2.CreateContainerRequest(
                mcp_id=mcp_id, user_id=user_id, type=type, env_vars=env_proto
            )
            response = self.stub.CreateContainer(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR Client] gRPC error in create_container: {str(e)}")
            # Assuming self._handle_error exists and is appropriate
            raise self._handle_error(e)

    async def stop_container(self, container_id: str) -> mcp_pb2.StopContainerResponse:
        """
        Sends a request to MCP service to stop a container.
        """
        try:
            request = mcp_pb2.StopContainerRequest(container_id=container_id)
            response = self.stub.StopContainer(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR Client] gRPC error in stop_container: {str(e)}")
            raise self._handle_error(e)

    async def get_container_status(self, container_id: str) -> mcp_pb2.GetContainerStatusResponse:
        """
        Sends a request to MCP service to get container status.
        """
        try:
            request = mcp_pb2.GetContainerStatusRequest(container_id=container_id)
            response = self.stub.GetContainerStatus(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR Client] gRPC error in get_container_status: {str(e)}")
            raise self._handle_error(e)

    async def delete_container(self, container_id: str) -> mcp_pb2.DeleteContainerResponse:
        """
        Sends a request to MCP service to delete a container.
        """
        try:
            request = mcp_pb2.DeleteContainerRequest(container_id=container_id)
            response = self.stub.DeleteContainer(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR Client] gRPC error in delete_container: {str(e)}")
            raise self._handle_error(e)

    async def update_tool_output_schema(
        self,
        mcp_id: str,
        tool_name: str,
        output_schema: Dict,
    ) -> mcp_pb2.MCPResponse:
        try:
            request = mcp_pb2.UpdateToolOutputSchemaRequest(
                mcp_id=mcp_id, tool_name=tool_name, output_schema_json=json.dumps(output_schema)
            )
            print(f"[DEBUG] Sending UpdateToolOutputSchemaRequest: {request}")
            return self.stub.UpdateToolOutputSchema(request)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def toggle_mcp_visibility(
        self, mcp_id: str, user_id: str
    ) -> mcp_pb2.ToggleMcpVisibilityResponse:
        """
        Calls the gRPC service to toggle MCP visibility.
        """
        try:
            request = mcp_pb2.ToggleMcpVisibilityRequest(
                mcp_id=mcp_id,
                user_id=user_id,
            )
            response = self.stub.ToggleMcpVisibility(request)

            return response

        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in toggle_mcp_visibility: {str(e)}")
            raise self._handle_error(e)

    async def update_mcp_env_vars(
        self,
        user_id: str,
        mcp_id: str,
        env_key_values: List[EnvKeyValue],  # Use your Pydantic model for type hint
    ) -> mcp_pb2.UpdateMcpEnvVarsResponse:  # Return raw protobuf response
        proto_kvs = [
            mcp_pb2.EnvVar(key=kv_pair.key, value=kv_pair.value) for kv_pair in env_key_values
        ]
        request = mcp_pb2.UpdateMcpEnvVarsRequest(
            user_id=user_id, mcp_id=mcp_id, env_key_values=proto_kvs
        )
        try:
            response = self.stub.UpdateMcpEnvVars(request)
            return response
        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in update_mcp_env_vars: {str(e)}")
            raise self._handle_error(e)

    async def get_mcp_env_vars(self, user_id: str, mcp_id: str) -> mcp_pb2.GetMcpEnvVarsResponse:
        request = mcp_pb2.GetMcpEnvVarsRequest(user_id=user_id, mcp_id=mcp_id)
        try:
            response = self.stub.GetMcpEnvVars(request)
            return response
        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in get_mcp_env_vars: {str(e)}")
            raise self._handle_error(e)

    async def refresh_mcp(self, mcp_id: str) -> mcp_pb2.MCPResponse:
        request = mcp_pb2.RefreshMCPRequest(mcp_id=mcp_id)
        try:
            response = self.stub.RefreshMCP(request)
            return response
        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in refresh_mcp: {str(e)}")
            raise self._handle_error(e)

    async def make_quick_tool(self, mcp_id: str, user_id: str) -> mcp_pb2.QuickToolResponse:
        """
        Makes an MCP a quick tool for a user.

        Args:
            mcp_id: The ID of the MCP to make a quick tool
            user_id: The ID of the user

        Returns:
            The gRPC QuickToolResponse
        """
        request = mcp_pb2.MakeQuickToolRequest(mcp_id=mcp_id, user_id=user_id)
        try:
            response = self.stub.makeQuickTool(request)
            return response
        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in make_quick_tool: {str(e)}")
            raise self._handle_error(e)

    async def remove_quick_tool(self, mcp_id: str, user_id: str) -> mcp_pb2.QuickToolResponse:
        """
        Removes an MCP from quick tools for a user.

        Args:
            mcp_id: The ID of the MCP to remove from quick tools
            user_id: The ID of the user

        Returns:
            The gRPC QuickToolResponse
        """
        request = mcp_pb2.RemoveQuickToolRequest(mcp_id=mcp_id, user_id=user_id)
        try:
            response = self.stub.removeQuickTool(request)
            return response
        except grpc.RpcError as e:
            print(f"[MCP_CLIENT] gRPC error in remove_quick_tool: {str(e)}")
            raise self._handle_error(e)
