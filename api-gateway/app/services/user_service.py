import grpc
from typing import List, Optional
from app.core.config import settings
from app.grpc_ import user_pb2, user_pb2_grpc
import requests
from app.schemas.user import GetAllUsersRequest


class UserServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.USER_SERVICE_HOST}:{settings.USER_SERVICE_PORT}"
        )

        self.stub = user_pb2_grpc.UserServiceStub(self.channel)

    async def register(self, email: str, password: str, full_name: str, organizationId: str):
        request = user_pb2.RegisterRequest(email=email, password=password, fullName=full_name, organizationId = organizationId)
        try:
            response = self.stub.register(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def verify_email_otp(self, otp_token: str):
        request = user_pb2.UpdateEmailVerificationDetails(token=otp_token)
        try:
            response = self.stub.updateEmailVerifiedDetails(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def login(self, email: str, password: str, fcm_token: Optional[str] = None):
        request = user_pb2.LoginRequest(
            email=email, password=password, fcmToken=fcm_token if fcm_token else ""
        )
        try:
            response = self.stub.login(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def google_oauth_login(self, auth_code: str):
        try:
            # Exchange authorization code for access token
            token_url = "https://oauth2.googleapis.com/token"

            # Parameters for token request
            token_data = {
                "client_id": settings.GOOGLE_CLIENT_ID,
                "client_secret": settings.GOOGLE_CLIENT_SECRET,
                "code": auth_code,
                "grant_type": "authorization_code",
                "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            }

            # Make the request to Google
            token_response = requests.post(token_url, data=token_data)
            token_json = token_response.json()

            if "error" in token_json:
                # Handle error in gRPC context
                raise Exception(f"Google OAuth error: {token_json['error']}")

            # Get access token from response
            access_token = token_json["access_token"]
            refresh_token = token_json["refresh_token"]

            # Use access token to get user info
            user_info_response = requests.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"},
            )
            user_info = user_info_response.json()

            # # Prepare gRPC request with user info
            request = user_pb2.GoogleOAuthRequest(
                email=user_info["email"],
                fullName=user_info["name"],
                googleId=user_info["id"],
                accessToken=access_token,
                refreshToken=refresh_token,
            )

            response = self.stub.googleOAuthLogin(request)
            return response

        except grpc.RpcError as e:
            print(e)
            raise self._handle_error(e)
        except Exception as e:
            # Handle other errors
            print(e)
            raise self._handle_error(f"OAuth process failed: {str(e)}")

    async def access_token(self, refresh_token: str):
        request = user_pb2.AccessTokenRequest(refreshToken=refresh_token)
        try:
            response = self.stub.accessToken(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def generate_reset_password_otp(self, email: str):
        request = user_pb2.ResetPasswordOTPRequest(email=email)
        try:
            response = self.stub.generateResetPasswordOTP(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_password(self, token: str, new_password: str, confirm_new_password: str):
        request = user_pb2.UpdatePasswordRequest(
            token=token, newPassword=new_password, ConfirmNewPassword=confirm_new_password
        )
        try:
            response = self.stub.updatePassword(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def reset_password(
        self, user_id: str, current_password: str, new_password: str, confirm_new_password: str
    ):
        request = user_pb2.ResetPasswordRequest(
            userId=user_id,
            currentPassword=current_password,
            newPassword=new_password,
            confirmNewPassword=confirm_new_password,
        )
        try:
            response = self.stub.resetPassword(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_all_users(self, allUserRequest: GetAllUsersRequest):
        request_data = {
            "page": allUserRequest.page,
            "pageSize": allUserRequest.page_size,
        }

        if allUserRequest.sort_by is not None:
            request_data["sortBy"] = allUserRequest.sort_by
        if allUserRequest.sort_order is not None:
            request_data["sortOrder"] = allUserRequest.sort_order
        if allUserRequest.is_email_verified is not None:
            request_data["isEmailVerified"] = allUserRequest.is_email_verified
        if allUserRequest.role is not None:
            request_data["role"] = allUserRequest.role
        if allUserRequest.is_active is not None:
            request_data["isActive"] = allUserRequest.is_active
        if allUserRequest.search is not None:
            request_data["search"] = allUserRequest.search

        request = user_pb2.GetAllUsersRequest(**request_data)

        try:
            response = self.stub.getAllUsers(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_user(self, user_id: str):
        request = user_pb2.GetUserRequest(userId=user_id)
        try:
            response = self.stub.getUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_user(
        self,
        user_id: str,
        full_name: Optional[str] = None,
        email: Optional[str] = None,
        password: Optional[str] = None,
        phone_number: Optional[str] = None,
        profile_image: Optional[str] = None,
        organizationId: Optional[str] = None
    ):
        request = user_pb2.UpdateUserRequest(
            userId=user_id, fullName=full_name, phoneNumber=phone_number, profileImage=profile_image, organizationId=organizationId
        )
        try:
            response = self.stub.updateUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_user(self, user_id: str):
        request = user_pb2.DeleteUserRequest(userId=user_id)
        try:
            response = self.stub.deleteUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_user_profile_details(
        self,
        user_id: str,
        company: Optional[str] = None,
        department: Optional[str] = None,
        job_role: Optional[str] = None,
    ):
        """
        Calls the updateUserProfileDetails RPC.
        """
        request = user_pb2.UpdateUserProfileDetailsRequest(
            userId=user_id,
            # Pass values only if they are not None
            company=company if company is not None else "",
            department=department if department is not None else "",
            jobRole=job_role if job_role is not None else "",
        )
        try:
            # Use async stub if your client needs to be async
            # response = await self.stub.updateUserProfileDetails(request)
            response = self.stub.updateUserProfileDetails(request)  # Use sync stub if appropriate
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    # --- New Waitlist Client Method ---
    async def add_to_waitlist(self, email: str) -> user_pb2.AddToWaitlistResponse:
        """Calls the AddToWaitlist gRPC method."""
        print(f"request received")
        request = user_pb2.AddToWaitlistRequest(email=email)
        try:
            response = self.stub.addToWaitlist(request)
            print(f"response received {response}")
            return response
        except grpc.RpcError as e:
            print(f"Error: {e}")
            raise self._handle_error(e)

    async def get_waitlist(
        self, page: int = 1, page_size: int = 10, status_filter: str = None
    ) -> user_pb2.GetWaitlistResponse:
        """Calls the GetWaitlist gRPC method."""
        request = user_pb2.GetWaitlistRequest(
            page=page, page_size=page_size, status_filter=status_filter
        )
        try:
            response = self.stub.getWaitlist(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def approve_waitlist_user(self, user_id: str) -> user_pb2.ApproveWaitlistUserResponse:
        """Calls the ApproveWaitlistUser gRPC method."""
        request = user_pb2.ApproveWaitlistUserRequest(id=user_id)
        try:
            response = self.stub.approveWaitlistUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def approve_multiple_waitlist_users(
        self, user_ids: List[str]
    ) -> user_pb2.ApproveMultipleWaitlistUsersResponse:
        """Calls the ApproveMultipleWaitlistUsers gRPC method."""
        request = user_pb2.ApproveMultipleWaitlistUsersRequest(ids=user_ids)
        try:
            response = self.stub.approveMultipleWaitlistUsers(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            from fastapi import HTTPException

            raise HTTPException(status_code=412, detail=details)

        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            from fastapi import HTTPException

            raise HTTPException(status_code=400, detail=details)
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")

    async def validate_user(self, user_id: str):
        """
        Validates a user using their user ID and returns detailed user information.

        Args:
            user_id (str): The ID of the user to validate

        Returns:
            dict: A dictionary containing:
                - success (bool): Whether the validation was successful
                - message (str): Status message
                - user (dict): User details including id, email, full_name, and fcm_token

        Raises:
            grpc.RpcError: If there is an error during the gRPC request
        """
        request = user_pb2.ValidateUserRequest(id=user_id)
        try:
            response = self.stub.validateUser(request)
            return {
                "success": response.success,
                "message": response.message,
                "user": (
                    {
                        "id": response.user.id,
                        "email": response.user.email,
                        "full_name": response.user.full_name,
                        "fcm_token": response.user.fcm_token,
                    }
                    if response.success
                    else None
                ),
            }
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_users_by_ids(self, user_ids: List[str]):
        """
        Retrieves multiple users by their IDs in a single call.

        This is a more efficient way to fetch user details for multiple users
        compared to making individual calls for each user.

        Args:
            user_ids (List[str]): List of user IDs to retrieve

        Returns:
            dict: A dictionary mapping user_id to user details (containing full_name)
        """
        # Remove duplicates to avoid unnecessary calls
        unique_user_ids = list(set(user_ids))

        if not unique_user_ids:
            return {}

        try:
            request = user_pb2.GetUsersByIdsRequest(user_ids=unique_user_ids)
            response = self.stub.getUsersByIds(request)

            if not response.success:
                print(f"[WARNING] Error fetching users by IDs: {response.message}")
                return {}

            # Convert the response to a dictionary
            user_details = {}
            for user in response.users:
                user_details[user.id] = {"full_name": user.full_name, "email": user.email}

            return user_details
        except grpc.RpcError as e:
            print(f"[WARNING] gRPC error fetching users by IDs: {str(e)}")
            return {}
        except Exception as e:
            print(f"[WARNING] Unexpected error fetching users by IDs: {str(e)}")
            return {}

    async def update_stripe_customer_id(
        self, user_id: str, stripe_customer_id: str
    ) -> user_pb2.UpdateStripeCustomerIdResponse:
        """Updates the Stripe Customer ID for a user."""
        request = user_pb2.UpdateStripeCustomerIdRequest(
            user_id=user_id, stripe_customer_id=stripe_customer_id
        )
        try:
            response = self.stub.updateStripeCustomerId(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def fetch_stripe_customer_id(
        self, user_id: str
    ) -> user_pb2.FetchStripeCustomerIdResponse:
        """Fetches the Stripe Customer ID for a user."""
        request = user_pb2.FetchStripeCustomerIdRequest(user_id=user_id)
        try:
            response = self.stub.fetchStripeCustomerId(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def generate_api_key(self, user_id: str, name: str, project: Optional[str] = None):
        request = user_pb2.GenerateAPIKeyRequest(user_id=user_id, name=name, project=project)
        try:
            print(f"request comming{request}")
            response = self.stub.GenerateAPIKey(request)
            print(f"response comming{response}")
            return response
        except grpc.RpcError as e:
            print(f"Error: {e}")
            raise self._handle_error(e)

    async def list_api_keys(self, user_id: str):
        request = user_pb2.ListAPIKeysRequest(user_id=user_id)
        try:
            response = self.stub.ListAPIKeys(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_api_key(self, user_id: str, key_id: str):
        request = user_pb2.DeleteAPIKeyRequest(user_id=user_id, key_id=key_id)
        try:
            response = self.stub.DeleteAPIKey(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_api_key_by_id(self, api_key_id: str, user_id: str):
        request = user_pb2.GetAPIKeyByIdRequest(api_key_id=api_key_id, user_id=user_id)
        try:
            # Change from getAPIKeyById to GetAPIKeyById
            response = self.stub.GetAPIKeyById(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_credential(
        self, owner_id: str, key_name: str, value: str, description: Optional[str] = None
    ):
        """Create credential using unified variable service"""
        return await self.create_variable(
            owner_id=owner_id,
            key_name=key_name,
            value=value,
            type="credential",
            description=description
        )

    async def get_credential(self, credential_id: str, owner_id: str):
        """Get credential using unified variable service"""
        return await self.get_variable(
            variable_id=credential_id,
            owner_id=owner_id,
            type="credential"
        )

    async def list_credentials(self, owner_id: str):
        """List credentials using unified variable service"""
        return await self.list_variables(
            owner_id=owner_id,
            type="credential"
        )

    async def delete_credential(self, credential_id: str, owner_id: str):
        """Delete credential using unified variable service"""
        return await self.delete_variable(
            variable_id=credential_id,
            owner_id=owner_id,
            type="credential"
        )

    async def update_credential(
        self,
        credential_id: str,
        owner_id: str,
        key_name: Optional[str] = None,
        value: Optional[str] = None,
        description: Optional[str] = None,
    ):
        """Update credential using unified variable service"""
        return await self.update_variable(
            variable_id=credential_id,
            owner_id=owner_id,
            type="credential",
            key_name=key_name,
            value=value,
            description=description
        )

    async def update_user_git_tokens(  # update git tokens
        self,
        user_id: str,
        access_token: str,
    ):
        request = user_pb2.UpdateUserGitToken(user_id=user_id, github_access_token=access_token)
        try:
            response = self.stub.updateUserGitToken(request)
            print(f"Response recived: {response.message}")
            return response
        except grpc.RpcError as e:
            print(f"Error updating token: {e}")
            raise self._handle_error(e)

    async def get_user_github_token(self, user_id: str) -> str:
        """
        Gets the decrypted GitHub access token for a user.

        Args:
            user_id: The user ID

        Returns:
            Decrypted GitHub access token

        Raises:
            HTTPException: If user not found or token cannot be decrypted
        """
        request = user_pb2.GetUserGitHubTokenRequest(user_id=user_id)
        try:
            response = self.stub.getUserGitHubToken(request)
            if not response.success:
                from fastapi import HTTPException

                raise HTTPException(status_code=404, detail=response.message)
            return response.access_token
        except grpc.RpcError as e:
            print(f"Error getting GitHub token: {e}")

    async def create_preference(
        self, user_id: str, provider: str, model: str, temperature: float, max_output_tokens: int
    ) -> user_pb2.CreatePreferenceResponse:
        """Calls the CreatePreference gRPC method."""
        request = user_pb2.CreatePreferenceRequest(
            user_id=user_id,
            provider=provider,
            model=model,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        try:
            response = self.stub.CreatePreference(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_preference(self, user_id: str) -> user_pb2.GetPreferenceResponse:
        """Calls the GetPreference gRPC method."""
        request = user_pb2.GetPreferenceRequest(user_id=user_id)
        try:
            response = self.stub.GetPreference(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_preference(
        self,
        user_id: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_output_tokens: Optional[int] = None,
    ) -> user_pb2.UpdatePreferenceResponse:
        """Calls the UpdatePreference gRPC method."""
        request_args = {"user_id": user_id}
        if provider is not None:
            request_args["provider"] = provider
        if model is not None:
            request_args["model"] = model
        if temperature is not None:
            request_args["temperature"] = temperature
        if max_output_tokens is not None:
            request_args["max_output_tokens"] = max_output_tokens

        request = user_pb2.UpdatePreferenceRequest(**request_args)
        try:
            response = self.stub.UpdatePreference(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_preferences(self, user_id: str) -> user_pb2.ListPreferencesResponse:
        """Calls the ListPreferences gRPC method."""
        request = user_pb2.ListPreferencesRequest(user_id=user_id)
        try:
            response = self.stub.ListPreferences(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def generate_organisation_tokens(self, organisation_id: str, user_email: str):
        """
        Generates access and refresh tokens for an organisation.
        
        Args:
            organisation_id (str): The ID of the organisation to generate tokens for
            
        Returns:
            Response containing access and refresh tokens
            
        Raises:
            HTTPException: If there is an error during token generation
        """
        request = user_pb2.GenerateOrganisationTokensRequest(organisation_id=organisation_id, user_email=user_email)
        try:
            response = self.stub.generateOrganisationTokens(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    # Variable Management Methods
    async def create_variable(self, owner_id: str, key_name: str, value: str, type: str, description: str = None):
        request = user_pb2.CreateVariableRequest(
            owner_id=owner_id,
            key_name=key_name,
            value=value,
            type=type,
            description=description
        )
        try:
            response = self.stub.create_variable(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_variables(self, owner_id: str, type: str):
        request = user_pb2.ListVariablesRequest(owner_id=owner_id, type=type)
        try:
            response = self.stub.list_variables(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_variable(self, variable_id: str, owner_id: str, type: str):
        request = user_pb2.GetVariableRequest(
            variable_id=variable_id,
            owner_id=owner_id,
            type=type
        )
        try:
            response = self.stub.get_variable(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_variable(self, variable_id: str, owner_id: str, type: str, key_name: str = None, value: str = None, description: str = None):
        request = user_pb2.UpdateVariableRequest(
            variable_id=variable_id,
            owner_id=owner_id,
            type=type,
            key_name=key_name,
            value=value,
            description=description
        )
        try:
            response = self.stub.update_variable(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_variable(self, variable_id: str, owner_id: str, type: str):
        request = user_pb2.DeleteVariableRequest(
            variable_id=variable_id,
            owner_id=owner_id,
            type=type
        )
        try:
            response = self.stub.delete_variable(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
