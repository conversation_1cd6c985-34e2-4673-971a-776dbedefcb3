"""
API Key Integration Tests for API Gateway

This module tests the complete API key integration flow from REST API
through gRPC client to authentication service.
"""

import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.services.authentication_service import get_auth_service_client
from app.schemas.integration import (
    APIKeyCredentialRequest,
    APIKeyCredentialResponse,
    APIKeyCredentialStoreResponse,
    APIKeyCredentialUpdateResponse,
    APIKeyCredentialDeleteResponse,
)


class TestAPIKeyIntegration:
    """Test API key integration endpoints and gRPC client methods."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_auth_service(self):
        """Mock authentication service client."""
        mock_service = AsyncMock()
        with patch('app.api.routers.integration_routes.get_auth_service_client', return_value=mock_service):
            yield mock_service

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return {"user_id": "test_user_123", "role": "user"}

    @pytest.fixture
    def sample_credentials(self):
        """Sample API key credentials."""
        return {
            "api_key": "sk-1234567890abcdef",
            "secret": "secret_value",
            "endpoint": "https://api.example.com"
        }

    @pytest.fixture
    def integration_id(self):
        """Sample integration ID."""
        return "550e8400-e29b-41d4-a716-446655440000"

    def test_store_api_key_credentials_success(self, client, mock_auth_service, mock_user, sample_credentials, integration_id):
        """Test successful API key credential storage."""
        # Mock authentication service response
        mock_auth_service.store_api_key_credentials.return_value = {
            "success": True,
            "message": "API key credentials stored successfully",
            "integration_id": integration_id,
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.post(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": sample_credentials}
            )

            # Verify response
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "API key credentials stored successfully"
            assert data["integration_id"] == integration_id

            # Verify gRPC client was called correctly
            mock_auth_service.store_api_key_credentials.assert_called_once_with(
                user_id="test_user_123",
                integration_id=integration_id,
                credentials=sample_credentials
            )

    def test_store_api_key_credentials_not_found(self, client, mock_auth_service, mock_user, sample_credentials, integration_id):
        """Test API key credential storage with integration not found."""
        # Mock authentication service response
        mock_auth_service.store_api_key_credentials.return_value = {
            "success": False,
            "message": "Integration not found",
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.post(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": sample_credentials}
            )

            # Verify response
            assert response.status_code == status.HTTP_404_NOT_FOUND
            data = response.json()
            assert "Integration not found" in data["detail"]

    def test_get_api_key_credentials_success(self, client, mock_auth_service, mock_user, sample_credentials, integration_id):
        """Test successful API key credential retrieval."""
        # Mock authentication service response
        mock_auth_service.get_api_key_credentials.return_value = {
            "success": True,
            "message": "API key credentials retrieved successfully",
            "user_id": "test_user_123",
            "integration_id": integration_id,
            "credentials": sample_credentials,
            "is_connected": True,
            "last_used_at": "2023-01-03T12:00:00",
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.get(f"/integrations/{integration_id}/api-key/credentials")

            # Verify response
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["user_id"] == "test_user_123"
            assert data["integration_id"] == integration_id
            assert data["credentials"] == sample_credentials
            assert data["is_connected"] is True
            assert data["last_used_at"] == "2023-01-03T12:00:00"

            # Verify gRPC client was called correctly
            mock_auth_service.get_api_key_credentials.assert_called_once_with(
                user_id="test_user_123",
                integration_id=integration_id
            )

    def test_get_api_key_credentials_not_found(self, client, mock_auth_service, mock_user, integration_id):
        """Test API key credential retrieval with credentials not found."""
        # Mock authentication service response
        mock_auth_service.get_api_key_credentials.return_value = {
            "success": False,
            "message": "API key credentials not found",
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.get(f"/integrations/{integration_id}/api-key/credentials")

            # Verify response
            assert response.status_code == status.HTTP_404_NOT_FOUND
            data = response.json()
            assert "API key credentials not found" in data["detail"]

    def test_update_api_key_credentials_success(self, client, mock_auth_service, mock_user, sample_credentials, integration_id):
        """Test successful API key credential update."""
        # Updated credentials
        updated_credentials = {
            "api_key": "sk-updated1234567890abcdef",
            "secret": "updated_secret_value",
            "endpoint": "https://api.updated.com"
        }

        # Mock authentication service response
        mock_auth_service.update_api_key_credentials.return_value = {
            "success": True,
            "message": "API key credentials updated successfully",
            "integration_id": integration_id,
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.put(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": updated_credentials}
            )

            # Verify response
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "API key credentials updated successfully"
            assert data["integration_id"] == integration_id

            # Verify gRPC client was called correctly
            mock_auth_service.update_api_key_credentials.assert_called_once_with(
                user_id="test_user_123",
                integration_id=integration_id,
                credentials=updated_credentials
            )

    def test_delete_api_key_credentials_success(self, client, mock_auth_service, mock_user, integration_id):
        """Test successful API key credential deletion."""
        # Mock authentication service response
        mock_auth_service.delete_api_key_credentials.return_value = {
            "success": True,
            "message": "API key credentials deleted successfully",
        }

        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request
            response = client.delete(f"/integrations/{integration_id}/api-key/credentials")

            # Verify response
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "API key credentials deleted successfully"

            # Verify gRPC client was called correctly
            mock_auth_service.delete_api_key_credentials.assert_called_once_with(
                user_id="test_user_123",
                integration_id=integration_id
            )

    def test_invalid_credentials_format(self, client, mock_user, integration_id):
        """Test API key credential storage with invalid credentials format."""
        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # Make request with invalid credentials (missing required field)
            response = client.post(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"invalid_field": "value"}
            )

            # Verify response
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_unauthorized_access(self, client, sample_credentials, integration_id):
        """Test API key endpoints without authentication."""
        # Mock authentication to raise exception
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.side_effect = Exception("Unauthorized")

            # Test store endpoint
            response = client.post(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": sample_credentials}
            )
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch('app.services.authentication_service.grpc')
    @patch('app.services.authentication_service.authentication_pb2')
    @patch('app.services.authentication_service.authentication_pb2_grpc')
    def test_grpc_client_methods(self, mock_grpc_stub, mock_pb2, mock_grpc, sample_credentials, integration_id):
        """Test gRPC client methods directly."""
        # Create mock gRPC components
        mock_channel = MagicMock()
        mock_stub_instance = MagicMock()
        mock_grpc.insecure_channel.return_value = mock_channel
        mock_grpc_stub.AuthenticationServiceStub.return_value = mock_stub_instance

        # Create authentication service client
        from app.services.authentication_service import AuthenticationServiceClient
        client = AuthenticationServiceClient()

        # Test store API key credentials
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "API key credentials stored successfully"
        mock_response.integration_id = integration_id
        mock_stub_instance.StoreAPIKeyCredentials.return_value = mock_response

        # Call the method
        import asyncio
        result = asyncio.run(client.store_api_key_credentials(
            user_id="test_user_123",
            integration_id=integration_id,
            credentials=sample_credentials
        ))

        # Verify result
        assert result["success"] is True
        assert result["message"] == "API key credentials stored successfully"
        assert result["integration_id"] == integration_id

        # Test get API key credentials
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "API key credentials retrieved successfully"
        mock_response.user_id = "test_user_123"
        mock_response.integration_id = integration_id
        mock_response.credentials = json.dumps(sample_credentials)
        mock_response.is_connected = True
        mock_response.last_used_at = "2023-01-03T12:00:00"
        mock_stub_instance.GetAPIKeyCredentials.return_value = mock_response

        # Call the method
        result = asyncio.run(client.get_api_key_credentials(
            user_id="test_user_123",
            integration_id=integration_id
        ))

        # Verify result
        assert result["success"] is True
        assert result["user_id"] == "test_user_123"
        assert result["integration_id"] == integration_id
        assert result["credentials"] == sample_credentials
        assert result["is_connected"] is True

    def test_complete_api_key_workflow(self, client, mock_auth_service, mock_user, sample_credentials, integration_id):
        """Test complete API key workflow: store -> get -> update -> delete."""
        # Mock authentication
        with patch('app.core.auth_guard.role_required') as mock_auth:
            mock_auth.return_value = lambda: mock_user

            # 1. Store credentials
            mock_auth_service.store_api_key_credentials.return_value = {
                "success": True,
                "message": "API key credentials stored successfully",
                "integration_id": integration_id,
            }

            response = client.post(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": sample_credentials}
            )
            assert response.status_code == status.HTTP_201_CREATED

            # 2. Get credentials
            mock_auth_service.get_api_key_credentials.return_value = {
                "success": True,
                "message": "API key credentials retrieved successfully",
                "user_id": "test_user_123",
                "integration_id": integration_id,
                "credentials": sample_credentials,
                "is_connected": True,
                "last_used_at": "2023-01-03T12:00:00",
            }

            response = client.get(f"/integrations/{integration_id}/api-key/credentials")
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["credentials"] == sample_credentials

            # 3. Update credentials
            updated_credentials = {"api_key": "updated_key", "secret": "updated_secret"}
            mock_auth_service.update_api_key_credentials.return_value = {
                "success": True,
                "message": "API key credentials updated successfully",
                "integration_id": integration_id,
            }

            response = client.put(
                f"/integrations/{integration_id}/api-key/credentials",
                json={"credentials": updated_credentials}
            )
            assert response.status_code == status.HTTP_200_OK

            # 4. Delete credentials
            mock_auth_service.delete_api_key_credentials.return_value = {
                "success": True,
                "message": "API key credentials deleted successfully",
            }

            response = client.delete(f"/integrations/{integration_id}/api-key/credentials")
            assert response.status_code == status.HTTP_200_OK

            # Verify all gRPC methods were called
            mock_auth_service.store_api_key_credentials.assert_called_once()
            mock_auth_service.get_api_key_credentials.assert_called_once()
            mock_auth_service.update_api_key_credentials.assert_called_once()
            mock_auth_service.delete_api_key_credentials.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])