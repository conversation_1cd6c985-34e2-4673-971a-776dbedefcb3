import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import HTT<PERSON>Exception
from fastapi.testclient import TestClient

from app.api.routers.agent_avatar_routes import avatar_router
from app.schemas.agent import AgentAvatarCreate
from app.utils.parse_user_details import extract_user_details_from_jwt


class TestAgentAvatarRoutes:
    """Test cases for agent avatar routes optimization."""

    @pytest.fixture
    def mock_current_user(self):
        """Mock current user from JWT token."""
        return {
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "role": "admin",
            "name": "<PERSON>",
            "organisation_id": "org123"
        }

    @pytest.fixture
    def expected_user_details(self):
        """Expected user details after transformation."""
        return {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "fcm_token": None
        }

    @pytest.fixture
    def mock_agent_service_response(self):
        """Mock successful agent service response."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Avatar created successfully"

        # Create a proper mock avatar that works with MessageToDict
        mock_avatar = Mock()
        mock_avatar.ListFields.return_value = []  # Empty fields for MessageToDict
        mock_avatar.id = "avatar123"
        mock_avatar.url = "https://example.com/avatar.png"
        mock_response.avatar = mock_avatar
        return mock_response

    @patch('app.api.routers.agent_avatar_routes.agent_service')
    @patch('app.api.routers.agent_avatar_routes.role_required')
    async def test_create_agent_avatar_uses_jwt_details(
        self, 
        mock_role_required, 
        mock_agent_service,
        mock_current_user,
        expected_user_details,
        mock_agent_service_response
    ):
        """Test that create_agent_avatar uses JWT details instead of gRPC call."""
        # Arrange
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.create_agent_avatar = AsyncMock(return_value=mock_agent_service_response)
        
        avatar_data = AgentAvatarCreate(url="https://example.com/avatar.png")

        # Act
        from app.api.routers.agent_avatar_routes import create_agent_avatar
        result = await create_agent_avatar(avatar_data, mock_current_user)

        # Assert
        # Verify that agent_service.create_agent_avatar was called with the correct user details
        mock_agent_service.create_agent_avatar.assert_called_once_with(
            url="https://example.com/avatar.png",
            owner_details=expected_user_details
        )
        
        # Verify the response
        assert result.success == True
        assert result.message == "Avatar created successfully"

    @patch('app.api.routers.agent_avatar_routes.agent_service')
    @patch('app.api.routers.agent_avatar_routes.role_required')
    async def test_delete_agent_avatar_uses_jwt_details(
        self, 
        mock_role_required, 
        mock_agent_service,
        mock_current_user,
        expected_user_details,
        mock_agent_service_response
    ):
        """Test that delete_agent_avatar uses JWT details instead of gRPC call."""
        # Arrange
        mock_role_required.return_value = lambda: mock_current_user
        mock_agent_service.delete_agent_avatar = AsyncMock(return_value=mock_agent_service_response)
        
        avatar_id = "avatar123"

        # Act
        from app.api.routers.agent_avatar_routes import delete_agent_avatar
        result = await delete_agent_avatar(avatar_id, mock_current_user)

        # Assert
        # Verify that agent_service.delete_agent_avatar was called with the correct user details
        mock_agent_service.delete_agent_avatar.assert_called_once_with(
            avatar_id="avatar123",
            owner_details=expected_user_details
        )
        
        # Verify the response
        assert result.success == True
        assert result.message == "Avatar created successfully"

    @patch('app.api.routers.agent_avatar_routes.user_service')
    async def test_no_user_service_calls_in_create_avatar(
        self,
        mock_user_service,
        mock_current_user,
        mock_agent_service_response
    ):
        """Test that user_service.validate_user is not called in create_agent_avatar."""
        # Arrange
        avatar_data = AgentAvatarCreate(url="https://example.com/avatar.png")

        with patch('app.api.routers.agent_avatar_routes.agent_service') as mock_agent_service:
            mock_agent_service.create_agent_avatar = AsyncMock(return_value=mock_agent_service_response)

            # Act
            from app.api.routers.agent_avatar_routes import create_agent_avatar
            await create_agent_avatar(avatar_data, mock_current_user)

        # Assert
        # Verify that user_service.validate_user was NOT called
        mock_user_service.validate_user.assert_not_called()

    @patch('app.api.routers.agent_avatar_routes.user_service')
    async def test_no_user_service_calls_in_delete_avatar(
        self,
        mock_user_service,
        mock_current_user
    ):
        """Test that user_service.validate_user is not called in delete_agent_avatar."""
        # Arrange
        avatar_id = "avatar123"

        # Create a proper mock response for delete operation
        mock_delete_response = Mock()
        mock_delete_response.success = True
        mock_delete_response.message = "Avatar deleted successfully"

        with patch('app.api.routers.agent_avatar_routes.agent_service') as mock_agent_service:
            mock_agent_service.delete_agent_avatar = AsyncMock(return_value=mock_delete_response)

            # Act
            from app.api.routers.agent_avatar_routes import delete_agent_avatar
            await delete_agent_avatar(avatar_id, mock_current_user)

        # Assert
        # Verify that user_service.validate_user was NOT called
        mock_user_service.validate_user.assert_not_called()

    def test_extract_user_details_integration(self, mock_current_user, expected_user_details):
        """Test that extract_user_details_from_jwt works correctly with real JWT data."""
        # Act
        result = extract_user_details_from_jwt(mock_current_user)

        # Assert
        assert result == expected_user_details

    @patch('app.api.routers.agent_avatar_routes.agent_service')
    async def test_create_avatar_error_handling(self, mock_agent_service, mock_current_user):
        """Test error handling in create_agent_avatar when agent service fails."""
        # Arrange
        mock_response = Mock()
        mock_response.success = False
        mock_response.message = "Avatar creation failed"
        mock_agent_service.create_agent_avatar = AsyncMock(return_value=mock_response)
        
        avatar_data = AgentAvatarCreate(url="https://example.com/avatar.png")

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            from app.api.routers.agent_avatar_routes import create_agent_avatar
            await create_agent_avatar(avatar_data, mock_current_user)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "Avatar creation failed"

    @patch('app.api.routers.agent_avatar_routes.agent_service')
    async def test_delete_avatar_error_handling(self, mock_agent_service, mock_current_user):
        """Test error handling in delete_agent_avatar when agent service fails."""
        # Arrange
        mock_response = Mock()
        mock_response.success = False
        mock_response.message = "Avatar deletion failed"
        mock_agent_service.delete_agent_avatar = AsyncMock(return_value=mock_response)
        
        avatar_id = "avatar123"

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            from app.api.routers.agent_avatar_routes import delete_agent_avatar
            await delete_agent_avatar(avatar_id, mock_current_user)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "Avatar deletion failed"
