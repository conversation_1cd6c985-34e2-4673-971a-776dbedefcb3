#!/usr/bin/env python3
"""
Integration tests for the enhanced credentials endpoint.
Tests that the /api/v1/credentials endpoint properly handles both credentials and global variables.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import HTTPException

# Import the modules to test
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app
from app.api.routers.credential_routes import credential_router
from app.services.user_service import UserServiceClient


class TestEnhancedCredentialsEndpoint:
    """Test cases for the enhanced credentials endpoint"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.client = TestClient(app)
        self.test_user = {
            "user_id": "test-user-123",
            "email": "<EMAIL>"
        }
        self.auth_headers = {
            "Authorization": "Bearer test-token"
        }
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_create_credential_backward_compatibility(self, mock_user_service, mock_role_required):
        """Test that creating credentials without type parameter still works (backward compatibility)"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable created successfully"
        mock_response.id = "cred-123"
        mock_response.key_name = "test_credential"
        
        mock_user_service.create_variable = AsyncMock(return_value=mock_response)
        
        # Test data
        credential_data = {
            "key_name": "test_credential",
            "value": "secret_value",
            "description": "Test credential"
        }
        
        # Execute request without type parameter
        response = self.client.post(
            "/api/v1/credentials",
            json=credential_data,
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["id"] == "cred-123"
        assert data["key_name"] == "test_credential"
        
        # Verify the service was called with default type "credential"
        mock_user_service.create_variable.assert_called_once_with(
            owner_id=self.test_user["user_id"],
            key_name="test_credential",
            value="secret_value",
            description="Test credential",
            type="credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_create_credential_explicit_type(self, mock_user_service, mock_role_required):
        """Test creating credential with explicit type=credential"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable created successfully"
        mock_response.id = "cred-456"
        mock_response.key_name = "explicit_credential"
        
        mock_user_service.create_variable = AsyncMock(return_value=mock_response)
        
        # Test data
        credential_data = {
            "key_name": "explicit_credential",
            "value": "secret_value",
            "description": "Explicit credential"
        }
        
        # Execute request with explicit type
        response = self.client.post(
            "/api/v1/credentials?type=credential",
            json=credential_data,
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["id"] == "cred-456"
        
        # Verify the service was called with explicit type
        mock_user_service.create_variable.assert_called_once_with(
            owner_id=self.test_user["user_id"],
            key_name="explicit_credential",
            value="secret_value",
            description="Explicit credential",
            type="credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_create_global_variable(self, mock_user_service, mock_role_required):
        """Test creating global variable with type=global-variable"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable created successfully"
        mock_response.id = "var-789"
        mock_response.key_name = "global_variable"
        
        mock_user_service.create_variable = AsyncMock(return_value=mock_response)
        
        # Test data
        variable_data = {
            "key_name": "global_variable",
            "value": "global_value",
            "description": "Global variable"
        }
        
        # Execute request with global-variable type
        response = self.client.post(
            "/api/v1/credentials?type=global-variable",
            json=variable_data,
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["id"] == "var-789"
        
        # Verify the service was called with global-variable type
        mock_user_service.create_variable.assert_called_once_with(
            owner_id=self.test_user["user_id"],
            key_name="global_variable",
            value="global_value",
            description="Global variable",
            type="global-variable"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_list_credentials_backward_compatibility(self, mock_user_service, mock_role_required):
        """Test listing credentials without type parameter (backward compatibility)"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_variable = Mock()
        mock_variable.id = "cred-123"
        mock_variable.key_name = "test_credential"
        mock_variable.description = "Test credential"
        mock_variable.value = ""  # Hidden for credentials
        mock_variable.created_at = "2023-01-01T00:00:00Z"
        mock_variable.updated_at = "2023-01-01T00:00:00Z"
        mock_variable.last_used_at = "2023-01-01T00:00:00Z"
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variables retrieved successfully"
        mock_response.variables = [mock_variable]
        
        mock_user_service.list_variables = AsyncMock(return_value=mock_response)
        
        # Execute request without type parameter
        response = self.client.get(
            "/api/v1/credentials",
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["credentials"]) == 1
        assert data["credentials"][0]["id"] == "cred-123"
        assert data["credentials"][0]["value"] == ""  # Should be hidden
        
        # Verify the service was called with default type "credential"
        mock_user_service.list_variables.assert_called_once_with(
            owner_id=self.test_user["user_id"],
            type="credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_list_global_variables(self, mock_user_service, mock_role_required):
        """Test listing global variables with type=global-variable"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_variable = Mock()
        mock_variable.id = "var-456"
        mock_variable.key_name = "global_var"
        mock_variable.description = "Global variable"
        mock_variable.value = "visible_value"  # Visible for global variables
        mock_variable.created_at = "2023-01-01T00:00:00Z"
        mock_variable.updated_at = "2023-01-01T00:00:00Z"
        mock_variable.last_used_at = "2023-01-01T00:00:00Z"
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variables retrieved successfully"
        mock_response.variables = [mock_variable]
        
        mock_user_service.list_variables = AsyncMock(return_value=mock_response)
        
        # Execute request with global-variable type
        response = self.client.get(
            "/api/v1/credentials?type=global-variable",
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["credentials"]) == 1  # Note: still uses "credentials" key for compatibility
        assert data["credentials"][0]["id"] == "var-456"
        assert data["credentials"][0]["value"] == "visible_value"  # Should be visible
        
        # Verify the service was called with global-variable type
        mock_user_service.list_variables.assert_called_once_with(
            owner_id=self.test_user["user_id"],
            type="global-variable"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_get_credential_by_id(self, mock_user_service, mock_role_required):
        """Test getting a specific credential by ID"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_variable = Mock()
        mock_variable.id = "cred-123"
        mock_variable.key_name = "test_credential"
        mock_variable.description = "Test credential"
        mock_variable.value = ""  # Hidden for credentials
        mock_variable.created_at = "2023-01-01T00:00:00Z"
        mock_variable.updated_at = "2023-01-01T00:00:00Z"
        mock_variable.last_used_at = "2023-01-01T00:00:00Z"
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable retrieved successfully"
        mock_response.variable = mock_variable
        
        mock_user_service.get_variable = AsyncMock(return_value=mock_response)
        
        # Execute request
        response = self.client.get(
            "/api/v1/credentials/cred-123?type=credential",
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["credential"]["id"] == "cred-123"
        assert data["credential"]["value"] == ""  # Should be hidden
        
        # Verify the service was called correctly
        mock_user_service.get_variable.assert_called_once_with(
            variable_id="cred-123",
            owner_id=self.test_user["user_id"],
            type="credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_update_credential(self, mock_user_service, mock_role_required):
        """Test updating a credential"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable updated successfully"
        mock_response.id = "cred-123"
        mock_response.key_name = "updated_credential"
        
        mock_user_service.update_variable = AsyncMock(return_value=mock_response)
        
        # Test data
        update_data = {
            "key_name": "updated_credential",
            "value": "new_secret_value",
            "description": "Updated credential"
        }
        
        # Execute request
        response = self.client.put(
            "/api/v1/credentials/cred-123?type=credential",
            json=update_data,
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["id"] == "cred-123"
        assert data["key_name"] == "updated_credential"
        
        # Verify the service was called correctly
        mock_user_service.update_variable.assert_called_once_with(
            variable_id="cred-123",
            owner_id=self.test_user["user_id"],
            type="credential",
            key_name="updated_credential",
            value="new_secret_value",
            description="Updated credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_delete_credential(self, mock_user_service, mock_role_required):
        """Test deleting a credential"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Variable deleted successfully"
        
        mock_user_service.delete_variable = AsyncMock(return_value=mock_response)
        
        # Execute request
        response = self.client.delete(
            "/api/v1/credentials/cred-123?type=credential",
            headers=self.auth_headers
        )
        
        # Verify
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Variable deleted successfully"
        
        # Verify the service was called correctly
        mock_user_service.delete_variable.assert_called_once_with(
            variable_id="cred-123",
            owner_id=self.test_user["user_id"],
            type="credential"
        )
    
    @patch('app.api.routers.credential_routes.role_required')
    @patch('app.api.routers.credential_routes.user_service')
    def test_error_handling(self, mock_user_service, mock_role_required):
        """Test error handling when service fails"""
        # Setup mocks
        mock_role_required.return_value = lambda: self.test_user
        
        mock_response = Mock()
        mock_response.success = False
        mock_response.message = "Variable not found"
        
        mock_user_service.get_variable = AsyncMock(return_value=mock_response)
        
        # Execute request
        response = self.client.get(
            "/api/v1/credentials/nonexistent?type=credential",
            headers=self.auth_headers
        )
        
        # Verify error response
        assert response.status_code == 404
        data = response.json()
        assert "Variable not found" in data["detail"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
