#!/usr/bin/env python3
"""
Test script to verify the updated marketplace /use endpoint functionality.
This script tests that the endpoint now returns agent/workflow data when using items.
"""

import json
from typing import Any, Dict

import requests

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust as needed
API_BASE = f"{BASE_URL}/marketplace"


def test_use_agent_endpoint():
    """Test the /use endpoint with an agent item."""
    print("Testing /use endpoint with AGENT item type...")

    # Sample request data
    request_data = {
        "item_id": "sample-agent-id",  # Replace with actual agent ID
        "item_type": "AGENT",
    }

    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE",  # Replace with actual token
    }

    try:
        response = requests.post(f"{API_BASE}/use", json=request_data, headers=headers)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")

            # Check if agent_id is returned
            agent_id = data.get("agent_id")
            if agent_id:
                print("✅ Agent ID returned successfully!")
                print(f"- Agent ID: {agent_id}")
            else:
                print("❌ No agent_id returned")

            # Check if workflow_id is None (should be for agent)
            workflow_id = data.get("workflow_id")
            if workflow_id is None:
                print("✅ Workflow ID correctly None for agent item")
            else:
                print("❌ Unexpected workflow_id for agent item")

        else:
            print(f"❌ Request failed: {response.text}")

    except Exception as e:
        print(f"❌ Error testing agent endpoint: {str(e)}")


def test_use_workflow_endpoint():
    """Test the /use endpoint with a workflow item."""
    print("\nTesting /use endpoint with WORKFLOW item type...")

    # Sample request data
    request_data = {
        "item_id": "sample-workflow-id",  # Replace with actual workflow ID
        "item_type": "WORKFLOW",
    }

    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE",  # Replace with actual token
    }

    try:
        response = requests.post(f"{API_BASE}/use", json=request_data, headers=headers)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")

            # Check if workflow_id is returned
            workflow_id = data.get("workflow_id")
            if workflow_id:
                print("✅ Workflow ID returned successfully!")
                print(f"- Workflow ID: {workflow_id}")
            else:
                print("❌ No workflow_id returned")

            # Check if agent_id is None (should be for workflow)
            agent_id = data.get("agent_id")
            if agent_id is None:
                print("✅ Agent ID correctly None for workflow item")
            else:
                print("❌ Unexpected agent_id for workflow item")

        else:
            print(f"❌ Request failed: {response.text}")

    except Exception as e:
        print(f"❌ Error testing workflow endpoint: {str(e)}")


def test_use_mcp_endpoint():
    """Test the /use endpoint with an MCP item."""
    print("\nTesting /use endpoint with MCP item type...")

    # Sample request data
    request_data = {"item_id": "sample-mcp-id", "item_type": "MCP"}  # Replace with actual MCP ID

    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE",  # Replace with actual token
    }

    try:
        response = requests.post(f"{API_BASE}/use", json=request_data, headers=headers)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")

            # Check that both agent_id and workflow_id are None for MCP
            agent_id = data.get("agent_id")
            workflow_id = data.get("workflow_id")

            if agent_id is None and workflow_id is None:
                print("✅ Agent ID and workflow ID correctly None for MCP item")
            else:
                print("❌ Unexpected agent_id or workflow_id for MCP item")

        else:
            print(f"❌ Request failed: {response.text}")

    except Exception as e:
        print(f"❌ Error testing MCP endpoint: {str(e)}")


def print_schema_info():
    """Print information about the expected response schema."""
    print("\n" + "=" * 60)
    print("EXPECTED RESPONSE SCHEMA")
    print("=" * 60)
    print(
        """
UseMarketplaceItemResponse:
{
    "success": bool,
    "message": str,
    "item_id": str,
    "item_type": "AGENT" | "WORKFLOW" | "MCP",
    "use_count": int,
    "agent_id": str | null,     // Only for AGENT items - ID of created/used agent
    "workflow_id": str | null   // Only for WORKFLOW items - ID of created/used workflow
}

For AGENT items:
- agent_id: Contains the ID of the created or used agent
- workflow_id: null

For WORKFLOW items:
- agent_id: null
- workflow_id: Contains the ID of the created or used workflow

For MCP items:
- agent_id: null
- workflow_id: null
    """
    )


if __name__ == "__main__":
    print("🧪 Testing Updated Marketplace /use Endpoint")
    print("=" * 50)

    print_schema_info()

    print("\n" + "=" * 50)
    print("RUNNING TESTS")
    print("=" * 50)

    # Note: You'll need to replace the sample IDs and add proper authentication
    print("⚠️  Note: Update the sample IDs and authentication token before running!")

    # Uncomment these lines when you have proper test data:
    # test_use_agent_endpoint()
    # test_use_workflow_endpoint()
    # test_use_mcp_endpoint()

    print("\n✅ Test script ready. Update configuration and uncomment test calls to run.")
