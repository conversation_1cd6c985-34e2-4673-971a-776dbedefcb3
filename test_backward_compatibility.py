#!/usr/bin/env python3
"""
Backward compatibility test for the enhanced credentials endpoint.
This script ensures that existing credential API calls continue to work unchanged.

Usage:
    python test_backward_compatibility.py
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"

class BackwardCompatibilityTest:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def setup_auth(self):
        """Setup authentication for API tests"""
        print("🔧 Setting up authentication...")
        self.session.headers.update({
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        })
        self.log_test("Authentication Setup", True, "Test token configured")
    
    def test_create_credential_legacy_format(self):
        """Test creating credential using legacy API format (no type parameter)"""
        print("\n🔄 Testing Legacy Credential Creation...")
        
        test_credential = {
            "key_name": "legacy_test_credential",
            "value": "legacy_secret_value",
            "description": "Legacy test credential"
        }
        
        try:
            # Test CREATE without any type parameter (legacy format)
            response = self.session.post(
                f"{API_BASE_URL}/credentials",
                json=test_credential
            )
            
            if response.status_code in [200, 201]:
                data = response.json()
                if data.get("success"):
                    credential_id = data.get("id")
                    self.log_test("Legacy Create Credential", True, f"Created credential: {credential_id}")
                    return credential_id
                else:
                    self.log_test("Legacy Create Credential", False, f"API returned success=false: {data.get('message')}")
            else:
                self.log_test("Legacy Create Credential", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Legacy Create Credential", False, f"Exception: {str(e)}")
        
        return None
    
    def test_list_credentials_legacy_format(self, expected_credential_id=None):
        """Test listing credentials using legacy API format (no type parameter)"""
        print("\n📋 Testing Legacy Credential Listing...")
        
        try:
            # Test LIST without any type parameter (legacy format)
            response = self.session.get(f"{API_BASE_URL}/credentials")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    credentials = data.get("credentials", [])
                    self.log_test("Legacy List Credentials", True, f"Retrieved {len(credentials)} credentials")
                    
                    # If we have an expected credential, verify it's in the list
                    if expected_credential_id:
                        found = any(cred.get("id") == expected_credential_id for cred in credentials)
                        self.log_test("Legacy List - Find Created Credential", found, 
                                    f"Credential {expected_credential_id} {'found' if found else 'not found'}")
                    
                    # Verify credential values are hidden (security check)
                    all_values_hidden = all(not cred.get("value") or cred.get("value") == "" for cred in credentials)
                    self.log_test("Legacy List - Values Hidden", all_values_hidden, 
                                "Credential values properly hidden for security")
                    
                    return credentials
                else:
                    self.log_test("Legacy List Credentials", False, f"API returned success=false: {data.get('message')}")
            else:
                self.log_test("Legacy List Credentials", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Legacy List Credentials", False, f"Exception: {str(e)}")
        
        return []
    
    def test_get_credential_legacy_format(self, credential_id):
        """Test getting specific credential using legacy API format"""
        if not credential_id:
            self.log_test("Legacy Get Credential", False, "No credential ID provided")
            return
        
        print(f"\n🔍 Testing Legacy Get Credential: {credential_id}...")
        
        try:
            # Test GET without type parameter (legacy format)
            response = self.session.get(f"{API_BASE_URL}/credentials/{credential_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    credential = data.get("credential")
                    if credential:
                        self.log_test("Legacy Get Credential", True, f"Retrieved credential: {credential.get('key_name')}")
                        
                        # Verify value is hidden
                        value_hidden = not credential.get("value") or credential.get("value") == ""
                        self.log_test("Legacy Get - Value Hidden", value_hidden, 
                                    "Credential value properly hidden")
                    else:
                        self.log_test("Legacy Get Credential", False, "No credential data in response")
                else:
                    self.log_test("Legacy Get Credential", False, f"API returned success=false: {data.get('message')}")
            else:
                self.log_test("Legacy Get Credential", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Legacy Get Credential", False, f"Exception: {str(e)}")
    
    def test_update_credential_legacy_format(self, credential_id):
        """Test updating credential using legacy API format"""
        if not credential_id:
            self.log_test("Legacy Update Credential", False, "No credential ID provided")
            return
        
        print(f"\n✏️ Testing Legacy Update Credential: {credential_id}...")
        
        update_data = {
            "key_name": "updated_legacy_credential",
            "value": "updated_secret_value",
            "description": "Updated legacy credential"
        }
        
        try:
            # Test UPDATE without type parameter (legacy format)
            response = self.session.put(
                f"{API_BASE_URL}/credentials/{credential_id}",
                json=update_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test("Legacy Update Credential", True, f"Updated credential: {data.get('key_name')}")
                else:
                    self.log_test("Legacy Update Credential", False, f"API returned success=false: {data.get('message')}")
            else:
                self.log_test("Legacy Update Credential", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Legacy Update Credential", False, f"Exception: {str(e)}")
    
    def test_delete_credential_legacy_format(self, credential_id):
        """Test deleting credential using legacy API format"""
        if not credential_id:
            self.log_test("Legacy Delete Credential", False, "No credential ID provided")
            return
        
        print(f"\n🗑️ Testing Legacy Delete Credential: {credential_id}...")
        
        try:
            # Test DELETE without type parameter (legacy format)
            response = self.session.delete(f"{API_BASE_URL}/credentials/{credential_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test("Legacy Delete Credential", True, "Credential deleted successfully")
                else:
                    self.log_test("Legacy Delete Credential", False, f"API returned success=false: {data.get('message')}")
            else:
                self.log_test("Legacy Delete Credential", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Legacy Delete Credential", False, f"Exception: {str(e)}")
    
    def test_api_response_format_compatibility(self):
        """Test that API response formats remain compatible with existing clients"""
        print("\n📋 Testing API Response Format Compatibility...")
        
        try:
            # Test that the response structure matches legacy expectations
            response = self.session.get(f"{API_BASE_URL}/credentials")
            
            if response.status_code == 200:
                data = response.json()
                
                # Check required fields exist
                has_success = "success" in data
                has_message = "message" in data
                has_credentials = "credentials" in data
                
                format_compatible = has_success and has_message and has_credentials
                self.log_test("Response Format Compatibility", format_compatible, 
                            "Response contains required fields: success, message, credentials")
                
                # Check credential object structure
                if data.get("credentials"):
                    cred = data["credentials"][0]
                    required_fields = ["id", "key_name", "description", "value", "created_at", "updated_at"]
                    has_all_fields = all(field in cred for field in required_fields)
                    self.log_test("Credential Object Structure", has_all_fields, 
                                "Credential objects contain all required fields")
                
            else:
                self.log_test("Response Format Compatibility", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Response Format Compatibility", False, f"Exception: {str(e)}")
    
    def test_error_handling_compatibility(self):
        """Test that error responses remain compatible"""
        print("\n⚠️ Testing Error Handling Compatibility...")
        
        try:
            # Test 404 error for non-existent credential
            response = self.session.get(f"{API_BASE_URL}/credentials/nonexistent-id")
            
            if response.status_code == 404:
                self.log_test("404 Error Handling", True, "Non-existent credential returns 404")
            else:
                self.log_test("404 Error Handling", False, f"Expected 404, got {response.status_code}")
            
            # Test invalid request format
            response = self.session.post(
                f"{API_BASE_URL}/credentials",
                json={"invalid": "data"}  # Missing required fields
            )
            
            if response.status_code in [400, 422]:  # Bad request or validation error
                self.log_test("Validation Error Handling", True, "Invalid data returns appropriate error")
            else:
                self.log_test("Validation Error Handling", False, f"Expected 400/422, got {response.status_code}")
                
        except Exception as e:
            self.log_test("Error Handling Compatibility", False, f"Exception: {str(e)}")
    
    def run_all_tests(self):
        """Run all backward compatibility tests"""
        print("🚀 Starting Backward Compatibility Tests")
        print("=" * 60)
        print("Testing that existing credential API calls continue to work unchanged")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Setup
        self.setup_auth()
        
        # Test full CRUD cycle using legacy API format
        credential_id = self.test_create_credential_legacy_format()
        credentials = self.test_list_credentials_legacy_format(credential_id)
        self.test_get_credential_legacy_format(credential_id)
        self.test_update_credential_legacy_format(credential_id)
        
        # Test API compatibility
        self.test_api_response_format_compatibility()
        self.test_error_handling_compatibility()
        
        # Cleanup
        self.test_delete_credential_legacy_format(credential_id)
        
        # Summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t["success"]])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 BACKWARD COMPATIBILITY TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.test_results:
                if not test["success"]:
                    print(f"  - {test['test']}: {test['message']}")
        
        if failed_tests == 0:
            print("\n🎉 All backward compatibility tests passed!")
            print("✅ Existing credential API calls continue to work unchanged")
            print("✅ Response formats remain compatible")
            print("✅ Error handling is preserved")
            print("✅ Security features (hidden values) are maintained")
        else:
            print("\n💥 Some backward compatibility tests failed!")
            print("⚠️  This could break existing client applications")
            print("🔧 Please fix the issues before deploying")
        
        return failed_tests == 0

def main():
    """Main test runner"""
    tester = BackwardCompatibilityTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Backward compatibility verified - safe to deploy!")
    else:
        print("\n❌ Backward compatibility issues detected - deployment not recommended!")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
