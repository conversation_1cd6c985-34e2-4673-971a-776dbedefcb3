#!/usr/bin/env python3
"""
Database migration to add 'type' column to existing credentials table.
This enables the unified variables system while maintaining backward compatibility.

Usage:
    python add_type_to_credentials.py migrate    # Apply migration
    python add_type_to_credentials.py rollback   # Rollback migration
    python add_type_to_credentials.py verify     # Verify migration
"""

import sys
import os
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, Enum
from sqlalchemy.exc import SQLAlchemyError

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.models.credential import CredentialType

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CredentialTypeMigration:
    def __init__(self):
        self.engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
        self.metadata = MetaData()
    
    def check_table_exists(self):
        """Check if credentials table exists"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'credentials'
                    );
                """))
                return result.scalar()
        except Exception as e:
            logger.error(f"Error checking table existence: {e}")
            return False
    
    def check_column_exists(self):
        """Check if type column already exists"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'public' 
                        AND table_name = 'credentials' 
                        AND column_name = 'type'
                    );
                """))
                return result.scalar()
        except Exception as e:
            logger.error(f"Error checking column existence: {e}")
            return False
    
    def migrate(self):
        """Apply the migration"""
        logger.info("🚀 Starting credentials table migration...")
        
        # Check if table exists
        if not self.check_table_exists():
            logger.error("❌ Credentials table does not exist. Please create the table first.")
            return False
        
        # Check if column already exists
        if self.check_column_exists():
            logger.info("✅ Type column already exists. Migration not needed.")
            return True
        
        try:
            with self.engine.begin() as conn:
                logger.info("📝 Adding type column to credentials table...")
                
                # Add the type column with default value
                conn.execute(text("""
                    ALTER TABLE credentials 
                    ADD COLUMN type VARCHAR(20) NOT NULL DEFAULT 'credential';
                """))
                
                logger.info("🔧 Creating enum constraint for type column...")
                
                # Add check constraint for enum values
                conn.execute(text("""
                    ALTER TABLE credentials 
                    ADD CONSTRAINT credentials_type_check 
                    CHECK (type IN ('credential', 'global-variable'));
                """))
                
                logger.info("📊 Updating existing records to have type 'credential'...")
                
                # Update existing records to have type 'credential' (they're all credentials)
                result = conn.execute(text("""
                    UPDATE credentials 
                    SET type = 'credential' 
                    WHERE type IS NULL OR type = '';
                """))
                
                updated_count = result.rowcount
                logger.info(f"✅ Updated {updated_count} existing records")
                
                logger.info("🎉 Migration completed successfully!")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"❌ Migration failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during migration: {e}")
            return False
    
    def rollback(self):
        """Rollback the migration"""
        logger.info("🔄 Starting migration rollback...")
        
        # Check if table exists
        if not self.check_table_exists():
            logger.error("❌ Credentials table does not exist.")
            return False
        
        # Check if column exists
        if not self.check_column_exists():
            logger.info("✅ Type column does not exist. Rollback not needed.")
            return True
        
        try:
            with self.engine.begin() as conn:
                logger.info("🗑️ Removing type column constraint...")
                
                # Drop the check constraint
                conn.execute(text("""
                    ALTER TABLE credentials 
                    DROP CONSTRAINT IF EXISTS credentials_type_check;
                """))
                
                logger.info("🗑️ Removing type column...")
                
                # Drop the type column
                conn.execute(text("""
                    ALTER TABLE credentials 
                    DROP COLUMN IF EXISTS type;
                """))
                
                logger.info("🎉 Rollback completed successfully!")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"❌ Rollback failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during rollback: {e}")
            return False
    
    def verify(self):
        """Verify the migration"""
        logger.info("🔍 Verifying migration...")
        
        # Check if table exists
        if not self.check_table_exists():
            logger.error("❌ Credentials table does not exist.")
            return False
        
        # Check if column exists
        if not self.check_column_exists():
            logger.error("❌ Type column does not exist.")
            return False
        
        try:
            with self.engine.connect() as conn:
                # Check column properties
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'credentials' 
                    AND column_name = 'type';
                """))
                
                column_info = result.fetchone()
                if column_info:
                    logger.info(f"✅ Type column exists:")
                    logger.info(f"   - Data type: {column_info[1]}")
                    logger.info(f"   - Nullable: {column_info[2]}")
                    logger.info(f"   - Default: {column_info[3]}")
                
                # Check constraint exists
                result = conn.execute(text("""
                    SELECT constraint_name 
                    FROM information_schema.table_constraints 
                    WHERE table_schema = 'public' 
                    AND table_name = 'credentials' 
                    AND constraint_name = 'credentials_type_check';
                """))
                
                constraint_exists = result.fetchone()
                if constraint_exists:
                    logger.info("✅ Type constraint exists")
                else:
                    logger.warning("⚠️ Type constraint does not exist")
                
                # Check data integrity
                result = conn.execute(text("""
                    SELECT type, COUNT(*) 
                    FROM credentials 
                    GROUP BY type;
                """))
                
                type_counts = result.fetchall()
                logger.info("📊 Current type distribution:")
                for type_name, count in type_counts:
                    logger.info(f"   - {type_name}: {count} records")
                
                logger.info("🎉 Migration verification completed!")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"❌ Verification failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during verification: {e}")
            return False

def main():
    """Main migration runner"""
    if len(sys.argv) != 2:
        print("Usage: python add_type_to_credentials.py [migrate|rollback|verify]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    migration = CredentialTypeMigration()
    
    if command == "migrate":
        success = migration.migrate()
    elif command == "rollback":
        success = migration.rollback()
    elif command == "verify":
        success = migration.verify()
    else:
        print("Invalid command. Use: migrate, rollback, or verify")
        sys.exit(1)
    
    if success:
        print(f"\n✅ {command.capitalize()} completed successfully!")
        sys.exit(0)
    else:
        print(f"\n❌ {command.capitalize()} failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
