#!/usr/bin/env python3
"""
Migration script to convert credentials table to unified variables table.
This script handles the migration from the old credentials system to the new unified variables system.

Usage:
    python migrate_credentials_to_variables.py migrate    # Run migration
    python migrate_credentials_to_variables.py verify     # Verify migration
    python migrate_credentials_to_variables.py rollback   # Rollback migration
    python migrate_credentials_to_variables.py cleanup    # Cleanup old table
"""

import sys
import os
import logging
from datetime import datetime
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, DateTime, Enum
from sqlalchemy.exc import SQLAlchemyError

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.models.variable import VariableType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CredentialToVariableMigration:
    def __init__(self):
        self.engine = create_engine(settings.DATABASE_URL)
        self.metadata = MetaData()
        
    def migrate(self):
        """Migrate credentials table to variables table"""
        logger.info("Starting migration from credentials to variables...")
        
        try:
            with self.engine.begin() as conn:
                # Step 1: Check if credentials table exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'credentials'
                    );
                """))
                
                if not result.scalar():
                    logger.error("Credentials table does not exist. Nothing to migrate.")
                    return False
                
                # Step 2: Check if variables table already exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'variables'
                    );
                """))
                
                if result.scalar():
                    logger.warning("Variables table already exists. Skipping table creation.")
                else:
                    # Step 3: Create variables table
                    logger.info("Creating variables table...")
                    conn.execute(text("""
                        CREATE TABLE variables (
                            id VARCHAR PRIMARY KEY,
                            key_name VARCHAR NOT NULL,
                            description VARCHAR,
                            value VARCHAR NOT NULL,
                            type VARCHAR(20) NOT NULL DEFAULT 'credential',
                            owner_id VARCHAR NOT NULL REFERENCES users(id),
                            created_at TIMESTAMP DEFAULT NOW(),
                            updated_at TIMESTAMP DEFAULT NOW(),
                            last_used_at TIMESTAMP DEFAULT NOW()
                        );
                    """))
                    
                    # Step 4: Create indexes
                    logger.info("Creating indexes...")
                    conn.execute(text("""
                        CREATE INDEX idx_variables_owner_type ON variables(owner_id, type);
                    """))
                    conn.execute(text("""
                        CREATE UNIQUE INDEX idx_variables_owner_key_type ON variables(owner_id, key_name, type);
                    """))
                
                # Step 5: Migrate data from credentials to variables
                logger.info("Migrating credential data...")
                result = conn.execute(text("""
                    SELECT COUNT(*) FROM credentials;
                """))
                credential_count = result.scalar()
                logger.info(f"Found {credential_count} credentials to migrate")
                
                if credential_count > 0:
                    # Check if data already migrated
                    result = conn.execute(text("""
                        SELECT COUNT(*) FROM variables WHERE type = 'credential';
                    """))
                    existing_count = result.scalar()
                    
                    if existing_count == 0:
                        conn.execute(text("""
                            INSERT INTO variables (id, key_name, description, value, type, owner_id, created_at, updated_at, last_used_at)
                            SELECT id, key_name, description, value, 'credential', owner_id, created_at, updated_at, last_used_at
                            FROM credentials;
                        """))
                        logger.info(f"Successfully migrated {credential_count} credentials")
                    else:
                        logger.warning(f"Found {existing_count} existing credential records in variables table. Skipping data migration.")
                
                logger.info("Migration completed successfully!")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Database error during migration: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during migration: {str(e)}")
            return False
    
    def verify(self):
        """Verify the migration was successful"""
        logger.info("Verifying migration...")
        
        try:
            with self.engine.begin() as conn:
                # Check if variables table exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'variables'
                    );
                """))
                
                if not result.scalar():
                    logger.error("Variables table does not exist!")
                    return False
                
                # Count records in both tables
                result = conn.execute(text("SELECT COUNT(*) FROM credentials;"))
                credential_count = result.scalar()
                
                result = conn.execute(text("SELECT COUNT(*) FROM variables WHERE type = 'credential';"))
                variable_count = result.scalar()
                
                logger.info(f"Credentials table: {credential_count} records")
                logger.info(f"Variables table (credentials): {variable_count} records")
                
                if credential_count == variable_count:
                    logger.info("✅ Migration verification successful!")
                    return True
                else:
                    logger.error("❌ Record count mismatch!")
                    return False
                    
        except SQLAlchemyError as e:
            logger.error(f"Database error during verification: {str(e)}")
            return False
    
    def rollback(self):
        """Rollback the migration (drop variables table)"""
        logger.warning("Rolling back migration...")
        
        try:
            with self.engine.begin() as conn:
                # Drop variables table
                conn.execute(text("DROP TABLE IF EXISTS variables CASCADE;"))
                logger.info("Variables table dropped successfully")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Database error during rollback: {str(e)}")
            return False
    
    def cleanup(self):
        """Cleanup old credentials table (DANGEROUS - only after verification)"""
        logger.warning("⚠️  CLEANUP: This will permanently delete the credentials table!")
        
        response = input("Are you sure you want to delete the credentials table? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Cleanup cancelled")
            return False
        
        try:
            with self.engine.begin() as conn:
                # Drop credentials table
                conn.execute(text("DROP TABLE IF EXISTS credentials CASCADE;"))
                logger.info("Credentials table dropped successfully")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Database error during cleanup: {str(e)}")
            return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python migrate_credentials_to_variables.py [migrate|verify|rollback|cleanup]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    migration = CredentialToVariableMigration()
    
    if command == "migrate":
        success = migration.migrate()
    elif command == "verify":
        success = migration.verify()
    elif command == "rollback":
        success = migration.rollback()
    elif command == "cleanup":
        success = migration.cleanup()
    else:
        print("Invalid command. Use: migrate, verify, rollback, or cleanup")
        sys.exit(1)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
