#!/usr/bin/env python3
"""
Check the current database structure without making changes.
"""

import sys
import os
import logging
from sqlalchemy import create_engine, text

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database_structure():
    """Check current database structure"""
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    
    try:
        with engine.connect() as conn:
            # Check if credentials table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'credentials'
                );
            """))
            table_exists = result.scalar()
            
            if table_exists:
                logger.info("✅ Credentials table exists")
                
                # Get table structure
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'credentials'
                    ORDER BY ordinal_position;
                """))
                
                columns = result.fetchall()
                logger.info("📋 Current credentials table structure:")
                for col in columns:
                    logger.info(f"   - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
                
                # Check if type column exists
                type_column_exists = any(col[0] == 'type' for col in columns)
                if type_column_exists:
                    logger.info("✅ Type column already exists")
                else:
                    logger.info("❌ Type column does not exist - migration needed")
                
                # Count existing records
                result = conn.execute(text("SELECT COUNT(*) FROM credentials;"))
                record_count = result.scalar()
                logger.info(f"📊 Current record count: {record_count}")
                
                if record_count > 0:
                    # Show sample data (without sensitive values)
                    result = conn.execute(text("""
                        SELECT id, key_name, description, 
                               CASE WHEN LENGTH(value) > 0 THEN '[ENCRYPTED]' ELSE '[EMPTY]' END as value_status,
                               created_at, updated_at
                        FROM credentials 
                        LIMIT 3;
                    """))
                    
                    sample_data = result.fetchall()
                    logger.info("📋 Sample records:")
                    for row in sample_data:
                        logger.info(f"   - ID: {row[0]}, Key: {row[1]}, Value: {row[3]}")
                
            else:
                logger.error("❌ Credentials table does not exist")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error checking database: {e}")
        return False
    
    return True

if __name__ == "__main__":
    check_database_structure()
