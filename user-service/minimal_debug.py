#!/usr/bin/env python3
"""
Minimal debug script to isolate the exact issue with list_variables.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_grpc_message_creation():
    """Test creating VariableInfo gRPC messages with real data"""
    
    print("🔧 Testing gRPC Message Creation")
    print("=" * 40)
    
    try:
        from app.grpc import user_pb2
        from datetime import datetime
        
        # Test creating a VariableInfo message with sample data
        print("Creating VariableInfo message...")
        
        # Sample data similar to what we get from database
        sample_data = {
            "id": "test-id-123",
            "key_name": "test_key",
            "description": "Test description",
            "type": "credential",
            "value": "",  # Empty for credentials
            "has_value": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "last_used_at": datetime.now()
        }
        
        # Try to create the message
        variable_info = user_pb2.VariableInfo(
            id=sample_data["id"],
            key_name=sample_data["key_name"],
            description=sample_data["description"],
            type=sample_data["type"],
            value=sample_data["value"],
            has_value=sample_data["has_value"],
            created_at=sample_data["created_at"].isoformat(),
            updated_at=sample_data["updated_at"].isoformat(),
            last_used_at=sample_data["last_used_at"].isoformat()
        )
        
        print("✅ VariableInfo message created successfully!")
        print(f"   ID: {variable_info.id}")
        print(f"   Key: {variable_info.key_name}")
        print(f"   Type: {variable_info.type}")
        print(f"   Has Value: {variable_info.has_value}")
        
        # Test creating a response with the message
        print("\nCreating ListVariablesResponse...")
        
        response = user_pb2.ListVariablesResponse(
            success=True,
            message="Test message",
            variables=[variable_info]
        )
        
        print("✅ ListVariablesResponse created successfully!")
        print(f"   Success: {response.success}")
        print(f"   Message: {response.message}")
        print(f"   Variables count: {len(response.variables)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating gRPC messages: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_record_processing():
    """Test processing actual database records"""
    
    print("\n🗄️ Testing Database Record Processing")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app.models.credential import Credential
        from app.grpc import user_pb2
        
        db = SessionLocal()
        
        # Get one actual credential record
        user_id = "c1454e90-09ac-40f2-bde2-833387d7b645"
        credential = db.query(Credential).filter(
            Credential.owner_id == user_id,
            Credential.type == "credential"
        ).first()
        
        if not credential:
            print("❌ No credential found for user")
            return False
        
        print(f"✅ Found credential: {credential.key_name}")
        print(f"   Type: {credential.type} (Python type: {type(credential.type)})")
        print(f"   Created: {credential.created_at} (Python type: {type(credential.created_at)})")
        print(f"   Updated: {credential.updated_at} (Python type: {type(credential.updated_at)})")
        print(f"   Last used: {credential.last_used_at} (Python type: {type(credential.last_used_at)})")
        
        # Test datetime serialization
        print("\nTesting datetime serialization...")
        try:
            created_iso = credential.created_at.isoformat()
            updated_iso = credential.updated_at.isoformat()
            last_used_iso = credential.last_used_at.isoformat()
            
            print(f"✅ Created ISO: {created_iso}")
            print(f"✅ Updated ISO: {updated_iso}")
            print(f"✅ Last used ISO: {last_used_iso}")
            
        except Exception as e:
            print(f"❌ Datetime serialization error: {str(e)}")
            return False
        
        # Test creating VariableInfo with real data
        print("\nTesting VariableInfo creation with real data...")
        try:
            variable_info = user_pb2.VariableInfo(
                id=credential.id,
                key_name=credential.key_name,
                description=credential.description or "",
                type=credential.type,
                value="",  # Empty for credentials
                has_value=bool(credential.value),
                created_at=credential.created_at.isoformat(),
                updated_at=credential.updated_at.isoformat(),
                last_used_at=credential.last_used_at.isoformat()
            )
            
            print("✅ VariableInfo created with real data!")
            
        except Exception as e:
            print(f"❌ VariableInfo creation error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database processing error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🚀 Minimal Debug for List Variables Issue")
    print("=" * 50)
    
    # Test 1: gRPC message creation
    grpc_success = test_grpc_message_creation()
    
    if grpc_success:
        # Test 2: Database record processing
        db_success = test_database_record_processing()
        
        if db_success:
            print("\n🎉 Both tests passed!")
            print("The issue might be in the service method logic or error handling.")
        else:
            print("\n💥 Database record processing failed.")
    else:
        print("\n💥 gRPC message creation failed.")
    
    return grpc_success and db_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
