from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime, Enum
from sqlalchemy.orm import relationship
from app.models.user import Base
import enum

class VariableType(enum.Enum):
    CREDENTIAL = "credential"
    GLOBAL_VARIABLE = "global-variable"

class Variable(Base):
    __tablename__ = "variables"

    id = Column(String, primary_key=True)
    key_name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    value = Column(String, nullable=False)  # Encrypted for credentials, plain for global vars
    type = Column(Enum(VariableType), nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    owner_id = Column(String, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used_at = Column(DateTime, default=datetime.utcnow)

    # Relationship with User
    owner = relationship("User", backref="variables")

    def __repr__(self):
        return f"<Variable {self.key_name} ({self.type.value})>"
