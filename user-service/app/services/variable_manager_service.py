import uuid
from datetime import datetime
import grpc
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.variable import Variable, VariableType
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc
import logging
from app.utils.secret_manager.secret_manager import EncryptionManager

class VariableService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
    
    def create_variable(
        self, request: user_pb2.CreateVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreateVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Creating variable: {request.key_name} of type: {request.type} for user: {request.owner_id}")
            
            # Verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.CreateVariableResponse(
                    success=False,
                    message="User not found"
                )
            
            # Check for duplicate key_name for this user and type
            existing_variable = db.query(Variable).filter(
                Variable.owner_id == request.owner_id,
                Variable.key_name == request.key_name,
                Variable.type == VariableType(request.type)
            ).first()
            
            if existing_variable:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Variable with this key_name already exists for type {request.type}")
                return user_pb2.CreateVariableResponse(
                    success=False,
                    message=f"Variable with this key_name already exists for type {request.type}"
                )

            # Process value based on type
            if request.type == "credential":
                # Create or get encryption key for user
                secret_id = self.encryption_manager._get_secret_name(request.owner_id)
                try:
                    # Try to get existing key
                    self.encryption_manager.get_user_encryption_key(request.owner_id)
                    logging.info(f"Using existing encryption key for user: {request.owner_id}")
                except ValueError:
                    # Create a new key if it doesn't exist
                    logging.info(f"Creating new encryption key for user: {request.owner_id}")
                    self.encryption_manager.create_and_store_user_key(secret_id)
                
                # Encrypt the value for credentials
                processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
            else:
                # Store as plain text for global variables
                processed_value = request.value

            variable = Variable(
                id=str(uuid.uuid4()),
                key_name=request.key_name,
                description=request.description if hasattr(request, "description") and request.description else None,
                value=processed_value,
                type=VariableType(request.type),
                owner_id=request.owner_id
            )

            db.add(variable)
            db.commit()
            db.refresh(variable)

            return user_pb2.CreateVariableResponse(
                success=True,
                message="Variable created successfully",
                id=variable.id,
                key_name=variable.key_name
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error creating variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.CreateVariableResponse(
                success=False,
                message=f"Failed to create variable: {str(e)}"
            )
        finally:
            db.close()
    
    def list_variables(
        self, request: user_pb2.ListVariablesRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListVariablesResponse:
        db = self.get_db()
        try:
            logging.info(f"Listing variables of type {request.type} for user: {request.owner_id}")
            
            # Verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.ListVariablesResponse(
                    success=False,
                    message="User not found"
                )

            # Get variables filtered by owner_id and type
            variables = db.query(Variable).filter(
                Variable.owner_id == request.owner_id,
                Variable.type == VariableType(request.type)
            ).all()
            
            logging.info(f"Found {len(variables)} variables for user")

            variable_list = []
            for var in variables:
                if var.type == VariableType.CREDENTIAL:
                    # Don't return actual value for credentials
                    variable_info = user_pb2.VariableInfo(
                        id=var.id,
                        key_name=var.key_name,
                        description=var.description or "",
                        type=var.type.value,
                        value="",  # Empty for credentials
                        has_value=bool(var.value),
                        created_at=var.created_at.isoformat(),
                        updated_at=var.updated_at.isoformat(),
                        last_used_at=var.last_used_at.isoformat()
                    )
                else:
                    # Return actual value for global variables
                    variable_info = user_pb2.VariableInfo(
                        id=var.id,
                        key_name=var.key_name,
                        description=var.description or "",
                        type=var.type.value,
                        value=var.value,  # Actual value for global variables
                        has_value=bool(var.value),
                        created_at=var.created_at.isoformat(),
                        updated_at=var.updated_at.isoformat(),
                        last_used_at=var.last_used_at.isoformat()
                    )
                
                variable_list.append(variable_info)

            return user_pb2.ListVariablesResponse(
                success=True,
                message="Variables retrieved successfully",
                variables=variable_list
            )
        except Exception as e:
            logging.error(f"Error listing variables: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.ListVariablesResponse(
                success=False,
                message=f"Failed to list variables: {str(e)}"
            )
        finally:
            db.close()
    
    def get_variable(
        self, request: user_pb2.GetVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Getting variable {request.variable_id} for user: {request.owner_id}")
            
            # Get variable by id, owner_id, and type
            variable = db.query(Variable).filter(
                Variable.id == request.variable_id,
                Variable.owner_id == request.owner_id,
                Variable.type == VariableType(request.type)
            ).first()
            
            if not variable:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.GetVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            if variable.type == VariableType.CREDENTIAL:
                # Don't return actual value for credentials
                variable_info = user_pb2.VariableInfo(
                    id=variable.id,
                    key_name=variable.key_name,
                    description=variable.description or "",
                    type=variable.type.value,
                    value="",  # Empty for credentials
                    has_value=bool(variable.value),
                    created_at=variable.created_at.isoformat(),
                    updated_at=variable.updated_at.isoformat(),
                    last_used_at=variable.last_used_at.isoformat()
                )
            else:
                # Return actual value for global variables
                variable_info = user_pb2.VariableInfo(
                    id=variable.id,
                    key_name=variable.key_name,
                    description=variable.description or "",
                    type=variable.type.value,
                    value=variable.value,  # Actual value for global variables
                    has_value=bool(variable.value),
                    created_at=variable.created_at.isoformat(),
                    updated_at=variable.updated_at.isoformat(),
                    last_used_at=variable.last_used_at.isoformat()
                )

            return user_pb2.GetVariableResponse(
                success=True,
                message="Variable retrieved successfully",
                variable=variable_info
            )
        except Exception as e:
            logging.error(f"Error getting variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.GetVariableResponse(
                success=False,
                message=f"Failed to get variable: {str(e)}"
            )
        finally:
            db.close()

    def update_variable(
        self, request: user_pb2.UpdateVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Updating variable {request.variable_id} for user: {request.owner_id}")

            # Get variable by id, owner_id, and type
            variable = db.query(Variable).filter(
                Variable.id == request.variable_id,
                Variable.owner_id == request.owner_id,
                Variable.type == VariableType(request.type)
            ).first()

            if not variable:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.UpdateVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            # Update fields if provided
            if hasattr(request, "key_name") and request.key_name:
                # Check for duplicate key_name for this user and type (excluding current variable)
                existing_variable = db.query(Variable).filter(
                    Variable.owner_id == request.owner_id,
                    Variable.key_name == request.key_name,
                    Variable.type == VariableType(request.type),
                    Variable.id != request.variable_id
                ).first()

                if existing_variable:
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                    context.set_details(f"Variable with this key_name already exists for type {request.type}")
                    return user_pb2.UpdateVariableResponse(
                        success=False,
                        message=f"Variable with this key_name already exists for type {request.type}"
                    )

                variable.key_name = request.key_name

            if hasattr(request, "description") and request.description is not None:
                variable.description = request.description

            if hasattr(request, "value") and request.value:
                # Process value based on type
                if variable.type == VariableType.CREDENTIAL:
                    # Encrypt the new value for credentials
                    processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
                else:
                    # Store as plain text for global variables
                    processed_value = request.value

                variable.value = processed_value

            variable.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(variable)

            return user_pb2.UpdateVariableResponse(
                success=True,
                message="Variable updated successfully",
                id=variable.id,
                key_name=variable.key_name
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error updating variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.UpdateVariableResponse(
                success=False,
                message=f"Failed to update variable: {str(e)}"
            )
        finally:
            db.close()

    def delete_variable(
        self, request: user_pb2.DeleteVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Deleting variable {request.variable_id} for user: {request.owner_id}")

            # Get variable by id, owner_id, and type
            variable = db.query(Variable).filter(
                Variable.id == request.variable_id,
                Variable.owner_id == request.owner_id,
                Variable.type == VariableType(request.type)
            ).first()

            if not variable:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.DeleteVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            db.delete(variable)
            db.commit()

            return user_pb2.DeleteVariableResponse(
                success=True,
                message="Variable deleted successfully"
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error deleting variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.DeleteVariableResponse(
                success=False,
                message=f"Failed to delete variable: {str(e)}"
            )
        finally:
            db.close()
