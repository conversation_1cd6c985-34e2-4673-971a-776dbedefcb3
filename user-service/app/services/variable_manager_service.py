import uuid
from datetime import datetime
import grpc
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.credential import Credential, CredentialType
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc
import logging
from app.utils.secret_manager.secret_manager import EncryptionManager

class VariableService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.encryption_manager = EncryptionManager()

    def _validate_credential_type(self, type_string: str) -> str:
        """Validate and return the credential type string"""
        if type_string in ["credential", "global-variable"]:
            return type_string
        else:
            raise ValueError(f"Invalid credential type: {type_string}")
    
    def get_db(self):
        return SessionLocal()
    
    def create_variable(
        self, request: user_pb2.CreateVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreateVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Creating variable: {request.key_name} of type: {request.type} for user: {request.owner_id}")
            
            # Verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.CreateVariableResponse(
                    success=False,
                    message="User not found"
                )
            
            # Check for duplicate key_name for this user and type
            existing_variable = db.query(Credential).filter(
                Credential.owner_id == request.owner_id,
                Credential.key_name == request.key_name,
                Credential.type == self._validate_credential_type(request.type)
            ).first()
            
            if existing_variable:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Variable with this key_name already exists for type {request.type}")
                return user_pb2.CreateVariableResponse(
                    success=False,
                    message=f"Variable with this key_name already exists for type {request.type}"
                )

            # Process value based on type
            if request.type == "credential":
                # Create or get encryption key for user
                secret_id = self.encryption_manager._get_secret_name(request.owner_id)
                try:
                    # Try to get existing key
                    self.encryption_manager.get_user_encryption_key(request.owner_id)
                    logging.info(f"Using existing encryption key for user: {request.owner_id}")
                except ValueError:
                    # Create a new key if it doesn't exist
                    logging.info(f"Creating new encryption key for user: {request.owner_id}")
                    self.encryption_manager.create_and_store_user_key(secret_id)
                
                # Encrypt the value for credentials
                processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
            else:
                # Store as plain text for global variables
                processed_value = request.value

            credential = Credential(
                id=str(uuid.uuid4()),
                key_name=request.key_name,
                description=request.description if hasattr(request, "description") and request.description else None,
                value=processed_value,
                type=self._validate_credential_type(request.type),
                owner_id=request.owner_id
            )

            db.add(credential)
            db.commit()
            db.refresh(credential)

            return user_pb2.CreateVariableResponse(
                success=True,
                message="Variable created successfully",
                id=credential.id,
                key_name=credential.key_name
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error creating variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.CreateVariableResponse(
                success=False,
                message=f"Failed to create variable: {str(e)}"
            )
        finally:
            db.close()
    
    def list_variables(
        self, request: user_pb2.ListVariablesRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListVariablesResponse:
        db = self.get_db()
        try:
            logging.info(f"Listing variables of type {request.type} for user: {request.owner_id}")
            
            # Verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.ListVariablesResponse(
                    success=False,
                    message="User not found"
                )

            # Get credentials filtered by owner_id and type
            credentials = db.query(Credential).filter(
                Credential.owner_id == request.owner_id,
                Credential.type == self._validate_credential_type(request.type)
            ).all()

            logging.info(f"Found {len(credentials)} credentials for user")

            variable_list = []
            for cred in credentials:
                if cred.type == "credential":
                    # Don't return actual value for credentials
                    variable_info = user_pb2.VariableInfo(
                        id=cred.id,
                        key_name=cred.key_name,
                        description=cred.description or "",
                        type=cred.type,
                        value="",  # Empty for credentials
                        has_value=bool(cred.value),
                        created_at=cred.created_at.isoformat(),
                        updated_at=cred.updated_at.isoformat(),
                        last_used_at=cred.last_used_at.isoformat()
                    )
                else:
                    # Return actual value for global variables
                    variable_info = user_pb2.VariableInfo(
                        id=cred.id,
                        key_name=cred.key_name,
                        description=cred.description or "",
                        type=cred.type,
                        value=cred.value,  # Actual value for global variables
                        has_value=bool(cred.value),
                        created_at=cred.created_at.isoformat(),
                        updated_at=cred.updated_at.isoformat(),
                        last_used_at=cred.last_used_at.isoformat()
                    )
                
                variable_list.append(variable_info)

            return user_pb2.ListVariablesResponse(
                success=True,
                message="Variables retrieved successfully",
                variables=variable_list
            )
        except Exception as e:
            logging.error(f"Error listing variables: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.ListVariablesResponse(
                success=False,
                message=f"Failed to list variables: {str(e)}"
            )
        finally:
            db.close()
    
    def get_variable(
        self, request: user_pb2.GetVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Getting variable {request.variable_id} for user: {request.owner_id}")
            
            # Get credential by id, owner_id, and type
            credential = db.query(Credential).filter(
                Credential.id == request.variable_id,
                Credential.owner_id == request.owner_id,
                Credential.type == self._validate_credential_type(request.type)
            ).first()

            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.GetVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            if credential.type == "credential":
                # Don't return actual value for credentials
                variable_info = user_pb2.VariableInfo(
                    id=credential.id,
                    key_name=credential.key_name,
                    description=credential.description or "",
                    type=credential.type,
                    value="",  # Empty for credentials
                    has_value=bool(credential.value),
                    created_at=credential.created_at.isoformat(),
                    updated_at=credential.updated_at.isoformat(),
                    last_used_at=credential.last_used_at.isoformat()
                )
            else:
                # Return actual value for global variables
                variable_info = user_pb2.VariableInfo(
                    id=credential.id,
                    key_name=credential.key_name,
                    description=credential.description or "",
                    type=credential.type,
                    value=credential.value,  # Actual value for global variables
                    has_value=bool(credential.value),
                    created_at=credential.created_at.isoformat(),
                    updated_at=credential.updated_at.isoformat(),
                    last_used_at=credential.last_used_at.isoformat()
                )

            return user_pb2.GetVariableResponse(
                success=True,
                message="Variable retrieved successfully",
                variable=variable_info
            )
        except Exception as e:
            logging.error(f"Error getting variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.GetVariableResponse(
                success=False,
                message=f"Failed to get variable: {str(e)}"
            )
        finally:
            db.close()

    def update_variable(
        self, request: user_pb2.UpdateVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Updating variable {request.variable_id} for user: {request.owner_id}")

            # Get credential by id, owner_id, and type
            credential = db.query(Credential).filter(
                Credential.id == request.variable_id,
                Credential.owner_id == request.owner_id,
                Credential.type == self._validate_credential_type(request.type)
            ).first()

            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.UpdateVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            # Update fields if provided
            if hasattr(request, "key_name") and request.key_name:
                # Check for duplicate key_name for this user and type (excluding current credential)
                existing_credential = db.query(Credential).filter(
                    Credential.owner_id == request.owner_id,
                    Credential.key_name == request.key_name,
                    Credential.type == self._validate_credential_type(request.type),
                    Credential.id != request.variable_id
                ).first()

                if existing_credential:
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                    context.set_details(f"Variable with this key_name already exists for type {request.type}")
                    return user_pb2.UpdateVariableResponse(
                        success=False,
                        message=f"Variable with this key_name already exists for type {request.type}"
                    )

                credential.key_name = request.key_name

            if hasattr(request, "description") and request.description is not None:
                credential.description = request.description

            if hasattr(request, "value") and request.value:
                # Process value based on type
                if credential.type == "credential":
                    # Encrypt the new value for credentials
                    processed_value = self.encryption_manager.encrypt(request.value, request.owner_id)
                else:
                    # Store as plain text for global variables
                    processed_value = request.value

                credential.value = processed_value

            credential.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(credential)

            return user_pb2.UpdateVariableResponse(
                success=True,
                message="Variable updated successfully",
                id=credential.id,
                key_name=credential.key_name
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error updating variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.UpdateVariableResponse(
                success=False,
                message=f"Failed to update variable: {str(e)}"
            )
        finally:
            db.close()

    def delete_variable(
        self, request: user_pb2.DeleteVariableRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteVariableResponse:
        db = self.get_db()
        try:
            logging.info(f"Deleting variable {request.variable_id} for user: {request.owner_id}")

            # Get credential by id, owner_id, and type
            credential = db.query(Credential).filter(
                Credential.id == request.variable_id,
                Credential.owner_id == request.owner_id,
                Credential.type == self._validate_credential_type(request.type)
            ).first()

            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Variable not found")
                return user_pb2.DeleteVariableResponse(
                    success=False,
                    message="Variable not found"
                )

            db.delete(credential)
            db.commit()

            return user_pb2.DeleteVariableResponse(
                success=True,
                message="Variable deleted successfully"
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Error deleting variable: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return user_pb2.DeleteVariableResponse(
                success=False,
                message=f"Failed to delete variable: {str(e)}"
            )
        finally:
            db.close()
