#!/usr/bin/env python3
"""
Debug script to test the list_variables method directly and see what's failing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import grpc
from app.services.variable_manager_service import VariableService
from app.grpc import user_pb2

def test_list_variables_directly():
    """Test the list_variables method directly to see the error"""
    
    print("🔧 Testing list_variables method directly")
    print("=" * 50)
    
    try:
        # Create the service
        service = VariableService()
        
        # Create a mock context
        context = grpc.ServicerContext()
        
        # Create the request
        request = user_pb2.ListVariablesRequest(
            owner_id="c1454e90-09ac-40f2-bde2-833387d7b645",
            type="credential"
        )
        
        print(f"Request:")
        print(f"  owner_id: {request.owner_id}")
        print(f"  type: {request.type}")
        
        # Call the method
        print(f"\nCalling list_variables...")
        response = service.list_variables(request, context)
        
        print(f"✅ Success! Response:")
        print(f"  success: {response.success}")
        print(f"  message: {response.message}")
        print(f"  variables count: {len(response.variables)}")
        
        for i, var in enumerate(response.variables):
            print(f"  Variable {i+1}:")
            print(f"    id: {var.id}")
            print(f"    key_name: {var.key_name}")
            print(f"    type: {var.type}")
            print(f"    has_value: {var.has_value}")
            print(f"    description: {var.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        print(f"Error type: {type(e)}")
        
        # Print full traceback
        import traceback
        print(f"\nFull traceback:")
        traceback.print_exc()
        
        return False

def test_database_query_directly():
    """Test the database query directly"""
    
    print("\n🗄️ Testing database query directly")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app.models.credential import Credential
        from app.models.user import User
        
        db = SessionLocal()
        
        user_id = "c1454e90-09ac-40f2-bde2-833387d7b645"
        
        # Check if user exists
        user = db.query(User).filter(User.id == user_id).first()
        print(f"User exists: {user is not None}")
        if user:
            print(f"User email: {user.email}")
        
        # Query credentials directly
        credentials = db.query(Credential).filter(
            Credential.owner_id == user_id,
            Credential.type == "credential"
        ).all()
        
        print(f"Found {len(credentials)} credentials")
        
        for i, cred in enumerate(credentials):
            print(f"Credential {i+1}:")
            print(f"  id: {cred.id}")
            print(f"  key_name: {cred.key_name}")
            print(f"  type: {cred.type} (type: {type(cred.type)})")
            print(f"  description: {cred.description}")
            print(f"  has value: {bool(cred.value)}")
            print(f"  created_at: {cred.created_at}")
            print(f"  updated_at: {cred.updated_at}")
            print(f"  last_used_at: {cred.last_used_at}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database query error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🚀 Debug List Credentials Issue")
    print("=" * 40)
    
    # Test database query first
    db_success = test_database_query_directly()
    
    if db_success:
        # Test service method
        service_success = test_list_variables_directly()
        
        if service_success:
            print("\n🎉 Both tests passed! The issue might be elsewhere.")
        else:
            print("\n💥 Service method failed. Check the error above.")
    else:
        print("\n💥 Database query failed. Check the database connection.")
    
    return db_success and service_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
