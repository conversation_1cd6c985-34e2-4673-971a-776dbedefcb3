#!/usr/bin/env python3
"""
Unit tests for the Variable Service in the User Service.
Tests the unified variable system for both credentials and global variables.
"""

import pytest
import uuid
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import grpc

# Import the modules to test
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.variable_manager_service import VariableService
from app.models.variable import Variable, VariableType
from app.models.user import User
from app.grpc import user_pb2


class TestVariableService:
    """Test cases for the VariableService class"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.variable_service = VariableService()
        self.mock_context = Mock(spec=grpc.ServicerContext)
        self.test_user_id = str(uuid.uuid4())
        self.test_variable_id = str(uuid.uuid4())
    
    @patch('app.services.variable_manager_service.SessionLocal')
    @patch('app.services.variable_manager_service.EncryptionManager')
    def test_create_credential_success(self, mock_encryption_manager, mock_session_local):
        """Test successful credential creation"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_user = Mock(spec=User)
        mock_user.id = self.test_user_id
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        
        mock_encryption = Mock()
        mock_encryption_manager.return_value = mock_encryption
        mock_encryption.encrypt.return_value = "encrypted_value"
        mock_encryption.get_user_encryption_key.return_value = "test_key"
        
        # Create request
        request = user_pb2.CreateVariableRequest(
            owner_id=self.test_user_id,
            key_name="test_credential",
            value="secret_value",
            type="credential",
            description="Test credential"
        )
        
        # Execute
        response = self.variable_service.create_variable(request, self.mock_context)
        
        # Verify
        assert response.success is True
        assert response.message == "Variable created successfully"
        assert response.key_name == "test_credential"
        mock_encryption.encrypt.assert_called_once_with("secret_value", self.test_user_id)
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_create_global_variable_success(self, mock_session_local):
        """Test successful global variable creation"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_user = Mock(spec=User)
        mock_user.id = self.test_user_id
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        
        # Create request
        request = user_pb2.CreateVariableRequest(
            owner_id=self.test_user_id,
            key_name="test_global_var",
            value="plain_value",
            type="global-variable",
            description="Test global variable"
        )
        
        # Execute
        response = self.variable_service.create_variable(request, self.mock_context)
        
        # Verify
        assert response.success is True
        assert response.message == "Variable created successfully"
        assert response.key_name == "test_global_var"
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_create_variable_user_not_found(self, mock_session_local):
        """Test variable creation when user doesn't exist"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Create request
        request = user_pb2.CreateVariableRequest(
            owner_id="nonexistent_user",
            key_name="test_var",
            value="value",
            type="credential"
        )
        
        # Execute
        response = self.variable_service.create_variable(request, self.mock_context)
        
        # Verify
        assert response.success is False
        assert response.message == "User not found"
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_create_variable_duplicate_key(self, mock_session_local):
        """Test variable creation with duplicate key name"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_user = Mock(spec=User)
        mock_user.id = self.test_user_id
        
        # Mock query chain for user lookup
        mock_user_query = Mock()
        mock_user_query.filter.return_value.first.return_value = mock_user
        
        # Mock query chain for duplicate check
        mock_existing_var = Mock(spec=Variable)
        mock_duplicate_query = Mock()
        mock_duplicate_query.filter.return_value.first.return_value = mock_existing_var
        
        # Setup query to return different results based on model
        def mock_query(model):
            if model == User:
                return mock_user_query
            elif model == Variable:
                return mock_duplicate_query
            return Mock()
        
        mock_db.query.side_effect = mock_query
        
        # Create request
        request = user_pb2.CreateVariableRequest(
            owner_id=self.test_user_id,
            key_name="duplicate_key",
            value="value",
            type="credential"
        )
        
        # Execute
        response = self.variable_service.create_variable(request, self.mock_context)
        
        # Verify
        assert response.success is False
        assert "already exists" in response.message
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.ALREADY_EXISTS)
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_list_credentials_success(self, mock_session_local):
        """Test successful credential listing"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_user = Mock(spec=User)
        mock_user.id = self.test_user_id
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        
        # Mock credentials
        mock_credential = Mock(spec=Variable)
        mock_credential.id = self.test_variable_id
        mock_credential.key_name = "test_cred"
        mock_credential.description = "Test credential"
        mock_credential.type = VariableType.CREDENTIAL
        mock_credential.value = "encrypted_value"
        mock_credential.created_at = datetime.now()
        mock_credential.updated_at = datetime.now()
        mock_credential.last_used_at = datetime.now()
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_credential]
        
        # Create request
        request = user_pb2.ListVariablesRequest(
            owner_id=self.test_user_id,
            type="credential"
        )
        
        # Execute
        response = self.variable_service.list_variables(request, self.mock_context)
        
        # Verify
        assert response.success is True
        assert len(response.variables) == 1
        assert response.variables[0].id == self.test_variable_id
        assert response.variables[0].key_name == "test_cred"
        assert response.variables[0].value == ""  # Should be empty for credentials
        assert response.variables[0].has_value is True
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_list_global_variables_success(self, mock_session_local):
        """Test successful global variable listing"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_user = Mock(spec=User)
        mock_user.id = self.test_user_id
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        
        # Mock global variable
        mock_variable = Mock(spec=Variable)
        mock_variable.id = self.test_variable_id
        mock_variable.key_name = "test_var"
        mock_variable.description = "Test variable"
        mock_variable.type = VariableType.GLOBAL_VARIABLE
        mock_variable.value = "plain_value"
        mock_variable.created_at = datetime.now()
        mock_variable.updated_at = datetime.now()
        mock_variable.last_used_at = datetime.now()
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_variable]
        
        # Create request
        request = user_pb2.ListVariablesRequest(
            owner_id=self.test_user_id,
            type="global-variable"
        )
        
        # Execute
        response = self.variable_service.list_variables(request, self.mock_context)
        
        # Verify
        assert response.success is True
        assert len(response.variables) == 1
        assert response.variables[0].id == self.test_variable_id
        assert response.variables[0].key_name == "test_var"
        assert response.variables[0].value == "plain_value"  # Should show actual value for global variables
        assert response.variables[0].has_value is True
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_delete_variable_success(self, mock_session_local):
        """Test successful variable deletion"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_variable = Mock(spec=Variable)
        mock_variable.id = self.test_variable_id
        mock_db.query.return_value.filter.return_value.first.return_value = mock_variable
        
        # Create request
        request = user_pb2.DeleteVariableRequest(
            variable_id=self.test_variable_id,
            owner_id=self.test_user_id,
            type="credential"
        )
        
        # Execute
        response = self.variable_service.delete_variable(request, self.mock_context)
        
        # Verify
        assert response.success is True
        assert response.message == "Variable deleted successfully"
        mock_db.delete.assert_called_once_with(mock_variable)
        mock_db.commit.assert_called_once()
    
    @patch('app.services.variable_manager_service.SessionLocal')
    def test_delete_variable_not_found(self, mock_session_local):
        """Test variable deletion when variable doesn't exist"""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value.__enter__.return_value = mock_db
        mock_session_local.return_value.__exit__.return_value = None
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Create request
        request = user_pb2.DeleteVariableRequest(
            variable_id="nonexistent_id",
            owner_id=self.test_user_id,
            type="credential"
        )
        
        # Execute
        response = self.variable_service.delete_variable(request, self.mock_context)
        
        # Verify
        assert response.success is False
        assert response.message == "Variable not found"
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
