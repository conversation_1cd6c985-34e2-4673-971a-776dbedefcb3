#!/usr/bin/env python3
"""
Test the exact loop logic from the service method.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_exact_service_loop():
    """Test the exact loop logic from list_variables method"""
    
    print("🔧 Testing Exact Service Loop Logic")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app.models.credential import Credential
        from app.services.variable_manager_service import VariableService
        from app.grpc import user_pb2
        
        db = SessionLocal()
        service = VariableService()
        
        user_id = 'c1454e90-09ac-40f2-bde2-833387d7b645'
        request_type = 'credential'
        
        # Get credentials (same as service)
        credentials = db.query(Credential).filter(
            Credential.owner_id == user_id,
            Credential.type == service._validate_credential_type(request_type)
        ).all()
        
        print(f"Found {len(credentials)} credentials")
        
        # Process each credential (exact same logic as service)
        variable_list = []
        for i, cred in enumerate(credentials):
            print(f"\nProcessing credential {i+1}: {cred.key_name}")
            
            try:
                if cred.type == "credential":
                    print("  Creating VariableInfo for credential...")
                    # Don't return actual value for credentials
                    variable_info = user_pb2.VariableInfo(
                        id=cred.id,
                        key_name=cred.key_name,
                        description=cred.description or "",
                        type=cred.type,
                        value="",  # Empty for credentials
                        has_value=bool(cred.value),
                        created_at=cred.created_at.isoformat(),
                        updated_at=cred.updated_at.isoformat(),
                        last_used_at=cred.last_used_at.isoformat()
                    )
                    print("  ✅ VariableInfo created successfully")
                else:
                    print("  Creating VariableInfo for global variable...")
                    # Return actual value for global variables
                    variable_info = user_pb2.VariableInfo(
                        id=cred.id,
                        key_name=cred.key_name,
                        description=cred.description or "",
                        type=cred.type,
                        value=cred.value,  # Actual value for global variables
                        has_value=bool(cred.value),
                        created_at=cred.created_at.isoformat(),
                        updated_at=cred.updated_at.isoformat(),
                        last_used_at=cred.last_used_at.isoformat()
                    )
                    print("  ✅ VariableInfo created successfully")
                
                variable_list.append(variable_info)
                print(f"  ✅ Added to list (total: {len(variable_list)})")
                
            except Exception as e:
                print(f"  ❌ Error processing credential {cred.key_name}: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
        
        print(f"\n✅ Successfully processed all {len(variable_list)} credentials")
        
        # Test creating the final response
        print("\nCreating final ListVariablesResponse...")
        try:
            response = user_pb2.ListVariablesResponse(
                success=True,
                message="Variables retrieved successfully",
                variables=variable_list
            )
            print("✅ ListVariablesResponse created successfully!")
            print(f"   Success: {response.success}")
            print(f"   Message: {response.message}")
            print(f"   Variables count: {len(response.variables)}")
            
        except Exception as e:
            print(f"❌ Error creating response: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Test Exact Service Loop")
    print("=" * 30)
    
    success = test_exact_service_loop()
    
    if success:
        print("\n🎉 Loop test passed!")
        print("The issue must be elsewhere in the service method.")
    else:
        print("\n💥 Loop test failed!")
        print("Found the exact issue in the loop logic.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
