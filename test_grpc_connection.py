#!/usr/bin/env python3
"""
Test gRPC connection between API Gateway and User Service
"""

import grpc
import sys
import os

# Add API Gateway path to import gRPC modules
sys.path.append('/Users/<USER>/Desktop/ruh_ai/backend/api-gateway')

try:
    from app.grpc_ import user_pb2, user_pb2_grpc
    print("✅ Successfully imported gRPC modules")
except ImportError as e:
    print(f"❌ Failed to import gRPC modules: {e}")
    sys.exit(1)

def test_grpc_connection():
    """Test gRPC connection to user service"""
    
    print("🔧 Testing gRPC Connection to User Service")
    print("=" * 50)
    
    # Test connection parameters
    host = "localhost"
    port = 50052
    
    print(f"Connecting to: {host}:{port}")
    
    try:
        # Create gRPC channel
        channel = grpc.insecure_channel(f"{host}:{port}")
        
        # Create stub
        stub = user_pb2_grpc.UserServiceStub(channel)
        
        print("✅ gRPC channel and stub created successfully")
        
        # Test connection with a simple health check or list call
        print("🔍 Testing actual gRPC call...")
        
        # Create a test request (we'll use list_variables since that's what's failing)
        request = user_pb2.ListVariablesRequest(
            owner_id="test-user-id",
            type="credential"
        )
        
        print("📤 Sending ListVariables request...")
        
        # Set a timeout for the call
        try:
            response = stub.ListVariables(request, timeout=5.0)
            print("✅ gRPC call successful!")
            print(f"Response: success={response.success}, message='{response.message}'")
            
        except grpc.RpcError as e:
            print(f"⚠️  gRPC call failed with RPC error:")
            print(f"   Status Code: {e.code()}")
            print(f"   Details: {e.details()}")
            
            if e.code() == grpc.StatusCode.UNAVAILABLE:
                print("❌ Service is unavailable - user service might not be running")
                return False
            elif e.code() == grpc.StatusCode.INTERNAL:
                print("⚠️  Internal server error - service is running but has issues")
                return True  # Connection works, but service has internal issues
            else:
                print("⚠️  Other gRPC error - connection might be working")
                return True
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create gRPC connection: {e}")
        return False
    
    finally:
        try:
            channel.close()
            print("🔒 gRPC channel closed")
        except:
            pass

def test_port_connectivity():
    """Test if the port is reachable"""
    import socket
    
    print("\n🌐 Testing Port Connectivity")
    print("=" * 30)
    
    host = "localhost"
    port = 50052
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open and reachable")
            return True
        else:
            print(f"❌ Port {port} is not reachable (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ Error testing port connectivity: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 gRPC Connection Diagnostic Tool")
    print("=" * 40)
    
    # Test 1: Port connectivity
    port_ok = test_port_connectivity()
    
    if not port_ok:
        print("\n💥 Port is not reachable. Make sure user service is running.")
        return False
    
    # Test 2: gRPC connection
    grpc_ok = test_grpc_connection()
    
    print("\n📋 Summary:")
    print(f"Port Connectivity: {'✅ OK' if port_ok else '❌ FAILED'}")
    print(f"gRPC Connection: {'✅ OK' if grpc_ok else '❌ FAILED'}")
    
    if port_ok and grpc_ok:
        print("\n🎉 Connection test successful!")
        print("The issue might be in the API Gateway's error handling or user service logic.")
    elif port_ok and not grpc_ok:
        print("\n⚠️  Port is reachable but gRPC connection failed.")
        print("Check if user service is properly implementing gRPC methods.")
    else:
        print("\n❌ Connection test failed.")
        print("Make sure user service is running on localhost:50052")
    
    return port_ok and grpc_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
