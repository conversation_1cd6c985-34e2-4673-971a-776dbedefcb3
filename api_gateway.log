cute_command
    conn = self.connection or pool.get_connection()
                              ~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/redis/utils.py", line 183, in wrapper
    return func(*args, **kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/redis/connection.py", line 1483, in get_connection
    connection.connect()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/redis/connection.py", line 384, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 61 connecting to 127.0.0.1:6379. Connection refused.
¸¸¸^CINFO:     Stopping reloader process [16731]
Pratham-ka-MacBook-Air:api-gateway prathamagarwal$ 
Pratham-ka-MacBook-Air:api-gateway prathamagarwal$ 
Pratham-ka-MacBook-Air:api-gateway prathamagarwal$ clear
Pratham-ka-MacBook-Air:api-gateway prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Repository already exists at: /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/proto-definitions, removing and cloning fresh copy
Cloning into 'proto-definitions'...
remote: Enumerating objects: 943, done.
remote: Counting objects: 100% (99/99), done.
remote: Compressing objects: 100% (99/99), done.
remote: Total 943 (delta 38), reused 2 (delta 0), pack-reused 844
Receiving objects: 100% (943/943), 236.58 KiB | 10.75 MiB/s, done.
Resolving deltas: 100% (500/500), done.
Successfully cloned fresh copy of repository: https://oauth2:<EMAIL>/ruh.ai/proto-definitions.git user-variables
Successfully generated gRPC code for user.proto
Successfully fixed imports in generated files for user.proto
Successfully generated gRPC code for admin.proto
Successfully fixed imports in generated files for admin.proto
Successfully generated gRPC code for notification.proto
Successfully fixed imports in generated files for notification.proto
communication.proto:7:1: warning: Import google/protobuf/any.proto is unused.
Successfully generated gRPC code for communication.proto
Successfully fixed imports in generated files for communication.proto
Successfully generated gRPC code for workflow.proto
Successfully fixed imports in generated files for workflow.proto
Successfully generated gRPC code for agent.proto
Successfully fixed imports in generated files for agent.proto
Successfully generated gRPC code for mcp.proto
Successfully fixed imports in generated files for mcp.proto
Successfully generated gRPC code for organisation.proto
Successfully fixed imports in generated files for organisation.proto
Successfully generated gRPC code for authentication.proto
Successfully fixed imports in generated files for authentication.proto
Successfully generated gRPC code for agent_graph.proto
Successfully fixed imports in generated files for agent_graph.proto
Successfully generated gRPC code for google_drive.proto
Successfully fixed imports in generated files for google_drive.proto
Successfully generated gRPC code for analytics.proto
Successfully fixed imports in generated files for analytics.proto
Successfully generated gRPC code for provider.proto
Successfully fixed imports in generated files for provider.proto
payment.proto:6:1: warning: Import google/protobuf/struct.proto is unused.
Successfully generated gRPC code for payment.proto
Successfully fixed imports in generated files for payment.proto
Starting API Gateway...
INFO:     Will watch for changes in these directories: ['/Users/<USER>/Desktop/ruh_ai/backend/api-gateway']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [17393] using StatReload
INFO:     Started server process [17410]
INFO:     Waiting for application startup.
API Gateway initialized
INFO:     Application startup complete.
INFO:     127.0.0.1:52669 - "GET /docs HTTP/1.1" 200 OK
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID register_api_v1_auth_register_post for function register at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID verify_email_otp_api_v1_auth_verify_email_otp_post for function verify_email_otp at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID login_api_v1_auth_login_post for function login at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID google_auth_api_v1_auth_google_login_get for function google_auth at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID google_callback_api_v1_auth_google_callback_get for function google_callback at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID access_token_api_v1_auth_access_token_post for function access_token at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID reset_password_otp_api_v1_auth_reset_password_otp_post for function reset_password_otp at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID update_password_api_v1_auth_update_password_post for function update_password at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID get_current_user_info_api_v1_users_me_get for function get_current_user_info at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID get_all_users_api_v1_users_list_users_get for function get_all_users at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID admin_login_api_v1_admin_auth_login_post for function admin_login at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/admin_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID admin_refresh_token_api_v1_admin_auth_access_token_post for function admin_refresh_token at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/admin_routes.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:52669 - "GET /api/v1/openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:56723 - "GET /api/v1/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56723 - "GET /api/v1/credentials HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56723 - "GET /api/v1/credentials?type=credential HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56723 - "GET /api/v1/credentials?type=global-variable HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56723 - "POST /api/v1/credentials HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56723 - "POST /api/v1/credentials?type=global-variable HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56723 - "GET /api/v1/openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:58160 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58716 - "GET /api/v1/credentials?type=credential HTTP/1.1" 401 Unauthorized
