#!/usr/bin/env python3
"""
Comprehensive test script for the unified variables system.
This script tests the complete flow from database migration to API endpoints.

Usage:
    python test_unified_variables.py
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_USER_TOKEN = None  # Will be set after login

class UnifiedVariablesTest:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def setup_auth(self):
        """Setup authentication for API tests"""
        # This would normally involve actual login
        # For testing purposes, we'll assume a valid token
        print("🔧 Setting up authentication...")
        # In a real scenario, you would:
        # 1. Create a test user
        # 2. Login and get JWT token
        # 3. Set Authorization header
        self.session.headers.update({
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        })
        self.log_test("Authentication Setup", True, "Test token configured")
    
    def test_database_migration(self):
        """Test database migration from credentials to variables"""
        print("\n📊 Testing Database Migration...")
        
        try:
            # Import the migration script
            sys.path.append('user-service/migrations')
            from migrate_credentials_to_variables import CredentialToVariableMigration
            
            migration = CredentialToVariableMigration()
            
            # Test migration
            success = migration.migrate()
            self.log_test("Database Migration", success, "Credentials table migrated to variables")
            
            # Test verification
            if success:
                verify_success = migration.verify()
                self.log_test("Migration Verification", verify_success, "Data integrity verified")
            
        except Exception as e:
            self.log_test("Database Migration", False, f"Error: {str(e)}")
    
    def test_credential_api_endpoints(self):
        """Test credential API endpoints"""
        print("\n🔐 Testing Credential API Endpoints...")
        
        # Test data
        test_credential = {
            "key_name": "test_api_key",
            "value": "sk-test-1234567890abcdef",
            "description": "Test API key for validation"
        }
        
        credential_id = None
        
        try:
            # Test CREATE credential
            response = self.session.post(
                f"{API_BASE_URL}/variables?type=credential",
                json=test_credential
            )
            
            if response.status_code == 201:
                data = response.json()
                credential_id = data.get("id")
                self.log_test("Create Credential", True, f"Created credential with ID: {credential_id}")
            else:
                self.log_test("Create Credential", False, f"Status: {response.status_code}")
                return
            
            # Test LIST credentials
            response = self.session.get(f"{API_BASE_URL}/variables?type=credential")
            if response.status_code == 200:
                data = response.json()
                credentials = data.get("variables", [])
                
                # Verify credential values are hidden
                hidden_values = all(cred.get("value") is None for cred in credentials)
                self.log_test("List Credentials", hidden_values, f"Found {len(credentials)} credentials, values hidden")
            else:
                self.log_test("List Credentials", False, f"Status: {response.status_code}")
            
            # Test GET specific credential
            if credential_id:
                response = self.session.get(f"{API_BASE_URL}/variables/{credential_id}?type=credential")
                if response.status_code == 200:
                    data = response.json()
                    credential = data.get("variable")
                    value_hidden = credential.get("value") is None
                    self.log_test("Get Credential", value_hidden, "Credential value properly hidden")
                else:
                    self.log_test("Get Credential", False, f"Status: {response.status_code}")
            
            # Test UPDATE credential
            if credential_id:
                update_data = {"value": "sk-updated-key-value"}
                response = self.session.put(
                    f"{API_BASE_URL}/variables/{credential_id}?type=credential",
                    json=update_data
                )
                success = response.status_code == 200
                self.log_test("Update Credential", success, "Credential updated successfully")
            
            # Test DELETE credential
            if credential_id:
                response = self.session.delete(f"{API_BASE_URL}/variables/{credential_id}?type=credential")
                success = response.status_code == 200
                self.log_test("Delete Credential", success, "Credential deleted successfully")
                
        except Exception as e:
            self.log_test("Credential API Tests", False, f"Error: {str(e)}")
    
    def test_global_variable_api_endpoints(self):
        """Test global variable API endpoints"""
        print("\n🌐 Testing Global Variable API Endpoints...")
        
        # Test data
        test_variable = {
            "key_name": "company_name",
            "value": "RUH AI Technologies",
            "description": "Company name for templates"
        }
        
        variable_id = None
        
        try:
            # Test CREATE global variable
            response = self.session.post(
                f"{API_BASE_URL}/variables?type=global-variable",
                json=test_variable
            )
            
            if response.status_code == 201:
                data = response.json()
                variable_id = data.get("id")
                self.log_test("Create Global Variable", True, f"Created variable with ID: {variable_id}")
            else:
                self.log_test("Create Global Variable", False, f"Status: {response.status_code}")
                return
            
            # Test LIST global variables
            response = self.session.get(f"{API_BASE_URL}/variables?type=global-variable")
            if response.status_code == 200:
                data = response.json()
                variables = data.get("variables", [])
                
                # Verify variable values are visible
                visible_values = all(var.get("value") is not None for var in variables)
                self.log_test("List Global Variables", visible_values, f"Found {len(variables)} variables, values visible")
            else:
                self.log_test("List Global Variables", False, f"Status: {response.status_code}")
            
            # Test GET specific global variable
            if variable_id:
                response = self.session.get(f"{API_BASE_URL}/variables/{variable_id}?type=global-variable")
                if response.status_code == 200:
                    data = response.json()
                    variable = data.get("variable")
                    value_visible = variable.get("value") is not None
                    self.log_test("Get Global Variable", value_visible, "Variable value properly visible")
                else:
                    self.log_test("Get Global Variable", False, f"Status: {response.status_code}")
            
            # Test UPDATE global variable
            if variable_id:
                update_data = {"value": "Updated Company Name"}
                response = self.session.put(
                    f"{API_BASE_URL}/variables/{variable_id}?type=global-variable",
                    json=update_data
                )
                success = response.status_code == 200
                self.log_test("Update Global Variable", success, "Variable updated successfully")
            
            # Test DELETE global variable
            if variable_id:
                response = self.session.delete(f"{API_BASE_URL}/variables/{variable_id}?type=global-variable")
                success = response.status_code == 200
                self.log_test("Delete Global Variable", success, "Variable deleted successfully")
                
        except Exception as e:
            self.log_test("Global Variable API Tests", False, f"Error: {str(e)}")
    
    def test_type_isolation(self):
        """Test that credentials and global variables are properly isolated"""
        print("\n🔒 Testing Type Isolation...")
        
        try:
            # Create one of each type with same key_name
            test_data = {
                "key_name": "test_isolation",
                "value": "test_value",
                "description": "Test isolation"
            }
            
            # Create credential
            cred_response = self.session.post(
                f"{API_BASE_URL}/variables?type=credential",
                json=test_data
            )
            
            # Create global variable
            var_response = self.session.post(
                f"{API_BASE_URL}/variables?type=global-variable",
                json=test_data
            )
            
            both_created = cred_response.status_code == 201 and var_response.status_code == 201
            self.log_test("Type Isolation - Creation", both_created, "Same key_name allowed for different types")
            
            if both_created:
                # Verify they appear in separate lists
                cred_list = self.session.get(f"{API_BASE_URL}/variables?type=credential").json()
                var_list = self.session.get(f"{API_BASE_URL}/variables?type=global-variable").json()
                
                cred_count = len([c for c in cred_list.get("variables", []) if c["key_name"] == "test_isolation"])
                var_count = len([v for v in var_list.get("variables", []) if v["key_name"] == "test_isolation"])
                
                isolation_success = cred_count == 1 and var_count == 1
                self.log_test("Type Isolation - Listing", isolation_success, "Variables properly isolated by type")
                
                # Cleanup
                if cred_response.status_code == 201:
                    cred_id = cred_response.json().get("id")
                    self.session.delete(f"{API_BASE_URL}/variables/{cred_id}?type=credential")
                
                if var_response.status_code == 201:
                    var_id = var_response.json().get("id")
                    self.session.delete(f"{API_BASE_URL}/variables/{var_id}?type=global-variable")
                
        except Exception as e:
            self.log_test("Type Isolation Tests", False, f"Error: {str(e)}")
    
    def test_security_features(self):
        """Test security features"""
        print("\n🛡️ Testing Security Features...")
        
        try:
            # Test that credential values are never returned in list operations
            test_credential = {
                "key_name": "security_test_cred",
                "value": "secret-value-should-be-hidden",
                "description": "Security test credential"
            }
            
            # Create credential
            response = self.session.post(
                f"{API_BASE_URL}/variables?type=credential",
                json=test_credential
            )
            
            if response.status_code == 201:
                credential_id = response.json().get("id")
                
                # List credentials and verify value is hidden
                list_response = self.session.get(f"{API_BASE_URL}/variables?type=credential")
                if list_response.status_code == 200:
                    credentials = list_response.json().get("variables", [])
                    test_cred = next((c for c in credentials if c["id"] == credential_id), None)
                    
                    if test_cred:
                        value_hidden = test_cred.get("value") is None
                        has_value_flag = test_cred.get("has_value") is True
                        
                        security_pass = value_hidden and has_value_flag
                        self.log_test("Credential Security", security_pass, "Values hidden, has_value flag set")
                    
                    # Cleanup
                    self.session.delete(f"{API_BASE_URL}/variables/{credential_id}?type=credential")
                
        except Exception as e:
            self.log_test("Security Tests", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Unified Variables System Tests")
        print("=" * 50)
        
        start_time = time.time()
        
        # Run tests
        self.setup_auth()
        self.test_database_migration()
        self.test_credential_api_endpoints()
        self.test_global_variable_api_endpoints()
        self.test_type_isolation()
        self.test_security_features()
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t["success"]])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.test_results:
                if not test["success"]:
                    print(f"  - {test['test']}: {test['message']}")
        
        return failed_tests == 0

def main():
    """Main test runner"""
    tester = UnifiedVariablesTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The unified variables system is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
