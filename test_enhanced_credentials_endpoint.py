#!/usr/bin/env python3
"""
Test script for the enhanced credentials endpoint that handles both credentials and global variables.
This script validates that the existing /api/v1/credentials endpoint now supports both types.

Usage:
    python test_enhanced_credentials_endpoint.py
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_USER_TOKEN = None  # Will be set after login

class EnhancedCredentialsEndpointTest:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def setup_auth(self):
        """Setup authentication for API tests"""
        print("🔧 Setting up authentication...")
        self.session.headers.update({
            "Authorization": "Bearer test-token",
            "Content-Type": "application/json"
        })
        self.log_test("Authentication Setup", True, "Test token configured")
    
    def test_backward_compatibility(self):
        """Test that existing credential endpoints still work without type parameter"""
        print("\n🔄 Testing Backward Compatibility...")
        
        test_credential = {
            "key_name": "test_backward_compat",
            "value": "test_value_123",
            "description": "Test backward compatibility"
        }
        
        try:
            # Test CREATE without type parameter (should default to credential)
            response = self.session.post(
                f"{API_BASE_URL}/credentials",
                json=test_credential
            )
            
            if response.status_code == 201:
                data = response.json()
                credential_id = data.get("id")
                self.log_test("Backward Compatibility - Create", True, f"Created credential: {credential_id}")
                
                # Test LIST without type parameter (should default to credential)
                response = self.session.get(f"{API_BASE_URL}/credentials")
                if response.status_code == 200:
                    data = response.json()
                    credentials = data.get("credentials", [])
                    found = any(c.get("id") == credential_id for c in credentials)
                    self.log_test("Backward Compatibility - List", found, f"Found credential in list")
                
                # Cleanup
                self.session.delete(f"{API_BASE_URL}/credentials/{credential_id}")
            else:
                self.log_test("Backward Compatibility - Create", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Backward Compatibility", False, f"Error: {str(e)}")
    
    def test_credential_type_explicit(self):
        """Test credential creation with explicit type=credential"""
        print("\n🔐 Testing Explicit Credential Type...")
        
        test_credential = {
            "key_name": "test_explicit_credential",
            "value": "secret_api_key_456",
            "description": "Test explicit credential type"
        }
        
        try:
            # Test CREATE with explicit type=credential
            response = self.session.post(
                f"{API_BASE_URL}/credentials?type=credential",
                json=test_credential
            )
            
            if response.status_code == 201:
                data = response.json()
                credential_id = data.get("id")
                self.log_test("Explicit Credential - Create", True, f"Created credential: {credential_id}")
                
                # Test LIST with type=credential
                response = self.session.get(f"{API_BASE_URL}/credentials?type=credential")
                if response.status_code == 200:
                    data = response.json()
                    credentials = data.get("credentials", [])
                    
                    # Verify credential values are hidden
                    test_cred = next((c for c in credentials if c.get("id") == credential_id), None)
                    if test_cred:
                        value_hidden = not test_cred.get("value") or test_cred.get("value") == ""
                        self.log_test("Explicit Credential - Value Hidden", value_hidden, "Credential value properly hidden")
                
                # Cleanup
                self.session.delete(f"{API_BASE_URL}/credentials/{credential_id}?type=credential")
            else:
                self.log_test("Explicit Credential - Create", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Explicit Credential Type", False, f"Error: {str(e)}")
    
    def test_global_variable_type(self):
        """Test global variable creation with type=global-variable"""
        print("\n🌐 Testing Global Variable Type...")
        
        test_variable = {
            "key_name": "test_global_var",
            "value": "global_value_789",
            "description": "Test global variable type"
        }
        
        try:
            # Test CREATE with type=global-variable
            response = self.session.post(
                f"{API_BASE_URL}/credentials?type=global-variable",
                json=test_variable
            )
            
            if response.status_code == 201:
                data = response.json()
                variable_id = data.get("id")
                self.log_test("Global Variable - Create", True, f"Created variable: {variable_id}")
                
                # Test LIST with type=global-variable
                response = self.session.get(f"{API_BASE_URL}/credentials?type=global-variable")
                if response.status_code == 200:
                    data = response.json()
                    variables = data.get("credentials", [])  # Note: still uses 'credentials' key for backward compatibility
                    
                    # Verify global variable values are visible
                    test_var = next((v for v in variables if v.get("id") == variable_id), None)
                    if test_var:
                        value_visible = test_var.get("value") == test_variable["value"]
                        self.log_test("Global Variable - Value Visible", value_visible, "Global variable value properly visible")
                
                # Cleanup
                self.session.delete(f"{API_BASE_URL}/credentials/{variable_id}?type=global-variable")
            else:
                self.log_test("Global Variable - Create", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Global Variable Type", False, f"Error: {str(e)}")
    
    def test_type_isolation(self):
        """Test that credentials and global variables are properly isolated"""
        print("\n🔒 Testing Type Isolation...")
        
        test_data = {
            "key_name": "test_isolation_key",
            "value": "test_value",
            "description": "Test type isolation"
        }
        
        try:
            # Create credential
            cred_response = self.session.post(
                f"{API_BASE_URL}/credentials?type=credential",
                json=test_data
            )
            
            # Create global variable with same key_name
            var_response = self.session.post(
                f"{API_BASE_URL}/credentials?type=global-variable",
                json=test_data
            )
            
            both_created = cred_response.status_code == 201 and var_response.status_code == 201
            self.log_test("Type Isolation - Creation", both_created, "Same key_name allowed for different types")
            
            if both_created:
                cred_id = cred_response.json().get("id")
                var_id = var_response.json().get("id")
                
                # Verify they appear in separate lists
                cred_list = self.session.get(f"{API_BASE_URL}/credentials?type=credential").json()
                var_list = self.session.get(f"{API_BASE_URL}/credentials?type=global-variable").json()
                
                cred_found = any(c.get("id") == cred_id for c in cred_list.get("credentials", []))
                var_found = any(v.get("id") == var_id for v in var_list.get("credentials", []))
                
                isolation_success = cred_found and var_found
                self.log_test("Type Isolation - Listing", isolation_success, "Variables properly isolated by type")
                
                # Cleanup
                self.session.delete(f"{API_BASE_URL}/credentials/{cred_id}?type=credential")
                self.session.delete(f"{API_BASE_URL}/credentials/{var_id}?type=global-variable")
                
        except Exception as e:
            self.log_test("Type Isolation", False, f"Error: {str(e)}")
    
    def test_crud_operations(self):
        """Test full CRUD operations for both types"""
        print("\n🔧 Testing CRUD Operations...")
        
        test_data = {
            "key_name": "test_crud_key",
            "value": "original_value",
            "description": "Test CRUD operations"
        }
        
        for var_type in ["credential", "global-variable"]:
            try:
                # CREATE
                response = self.session.post(
                    f"{API_BASE_URL}/credentials?type={var_type}",
                    json=test_data
                )
                
                if response.status_code == 201:
                    item_id = response.json().get("id")
                    self.log_test(f"CRUD {var_type} - Create", True, f"Created {var_type}")
                    
                    # READ
                    response = self.session.get(f"{API_BASE_URL}/credentials/{item_id}?type={var_type}")
                    read_success = response.status_code == 200
                    self.log_test(f"CRUD {var_type} - Read", read_success, f"Retrieved {var_type}")
                    
                    # UPDATE
                    update_data = {"value": "updated_value"}
                    response = self.session.put(
                        f"{API_BASE_URL}/credentials/{item_id}?type={var_type}",
                        json=update_data
                    )
                    update_success = response.status_code == 200
                    self.log_test(f"CRUD {var_type} - Update", update_success, f"Updated {var_type}")
                    
                    # DELETE
                    response = self.session.delete(f"{API_BASE_URL}/credentials/{item_id}?type={var_type}")
                    delete_success = response.status_code == 200
                    self.log_test(f"CRUD {var_type} - Delete", delete_success, f"Deleted {var_type}")
                else:
                    self.log_test(f"CRUD {var_type}", False, f"Failed to create {var_type}")
                    
            except Exception as e:
                self.log_test(f"CRUD {var_type}", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Enhanced Credentials Endpoint Tests")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Run tests
        self.setup_auth()
        self.test_backward_compatibility()
        self.test_credential_type_explicit()
        self.test_global_variable_type()
        self.test_type_isolation()
        self.test_crud_operations()
        
        # Summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t["success"]])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.test_results:
                if not test["success"]:
                    print(f"  - {test['test']}: {test['message']}")
        
        return failed_tests == 0

def main():
    """Main test runner"""
    tester = EnhancedCredentialsEndpointTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The enhanced credentials endpoint is working correctly.")
        print("✅ Backward compatibility maintained")
        print("✅ Both credentials and global variables supported")
        print("✅ Type isolation working properly")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
