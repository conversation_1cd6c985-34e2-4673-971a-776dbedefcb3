#!/usr/bin/env python3
"""
Comprehensive test runner for the unified variables system.
This script runs all tests to ensure nothing breaks with the new implementation.

Usage:
    python run_all_tests.py
"""

import subprocess
import sys
import os
from datetime import datetime
import json

class TestRunner:
    def __init__(self):
        self.test_results = []
        self.start_time = datetime.now()
    
    def log_result(self, test_name, success, message="", details=None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
    
    def run_command(self, command, cwd=None, description=""):
        """Run a shell command and return success status"""
        try:
            print(f"\n🔧 Running: {description}")
            print(f"Command: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                self.log_result(description, True, "Command executed successfully")
                if result.stdout:
                    print(f"Output: {result.stdout[:500]}...")  # Show first 500 chars
                return True
            else:
                self.log_result(description, False, f"Command failed with code {result.returncode}", {
                    "stdout": result.stdout,
                    "stderr": result.stderr
                })
                print(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.log_result(description, False, "Command timed out after 5 minutes")
            return False
        except Exception as e:
            self.log_result(description, False, f"Exception: {str(e)}")
            return False
    
    def check_file_exists(self, file_path, description=""):
        """Check if a file exists"""
        exists = os.path.exists(file_path)
        self.log_result(f"File Check: {description}", exists, f"File: {file_path}")
        return exists
    
    def run_unit_tests(self):
        """Run unit tests for user service"""
        print("\n" + "="*60)
        print("🧪 RUNNING UNIT TESTS")
        print("="*60)
        
        # Check if test files exist
        user_service_test = "user-service/tests/test_variable_service.py"
        api_gateway_test = "api-gateway/tests/test_enhanced_credentials_endpoint.py"
        
        self.check_file_exists(user_service_test, "User Service Variable Tests")
        self.check_file_exists(api_gateway_test, "API Gateway Enhanced Endpoint Tests")
        
        # Run user service tests
        if os.path.exists(user_service_test):
            success = self.run_command(
                f"cd user-service && python -m pytest tests/test_variable_service.py -v",
                description="User Service Variable Tests"
            )
        else:
            self.log_result("User Service Variable Tests", False, "Test file not found")
        
        # Run API gateway tests
        if os.path.exists(api_gateway_test):
            success = self.run_command(
                f"cd api-gateway && python -m pytest tests/test_enhanced_credentials_endpoint.py -v",
                description="API Gateway Enhanced Endpoint Tests"
            )
        else:
            self.log_result("API Gateway Enhanced Endpoint Tests", False, "Test file not found")
    
    def check_proto_compilation(self):
        """Check if proto files can be compiled"""
        print("\n" + "="*60)
        print("🔧 CHECKING PROTO COMPILATION")
        print("="*60)
        
        # Check if proto files exist
        proto_file = "proto-definitions/user.proto"
        self.check_file_exists(proto_file, "User Proto Definition")
        
        if os.path.exists(proto_file):
            # Try to compile proto (if protoc is available)
            success = self.run_command(
                f"protoc --python_out=. proto-definitions/user.proto",
                description="Proto Compilation Check"
            )
        else:
            self.log_result("Proto Compilation Check", False, "Proto file not found")
    
    def check_database_migration(self):
        """Check database migration script"""
        print("\n" + "="*60)
        print("🗄️ CHECKING DATABASE MIGRATION")
        print("="*60)
        
        migration_script = "user-service/migrations/migrate_credentials_to_variables.py"
        self.check_file_exists(migration_script, "Database Migration Script")
        
        if os.path.exists(migration_script):
            # Test migration script syntax
            success = self.run_command(
                f"cd user-service && python -m py_compile migrations/migrate_credentials_to_variables.py",
                description="Migration Script Syntax Check"
            )
        else:
            self.log_result("Migration Script Syntax Check", False, "Migration script not found")
    
    def check_api_gateway_routes(self):
        """Check API gateway route configuration"""
        print("\n" + "="*60)
        print("🌐 CHECKING API GATEWAY ROUTES")
        print("="*60)
        
        # Check if enhanced credential routes exist
        credential_routes = "api-gateway/app/api/routers/credential_routes.py"
        self.check_file_exists(credential_routes, "Enhanced Credential Routes")
        
        if os.path.exists(credential_routes):
            # Check syntax
            success = self.run_command(
                f"cd api-gateway && python -m py_compile app/api/routers/credential_routes.py",
                description="Credential Routes Syntax Check"
            )
            
            # Check if variable routes were removed (should not exist)
            variable_routes = "api-gateway/app/api/routers/variable_routes.py"
            if os.path.exists(variable_routes):
                self.log_result("Variable Routes Cleanup", False, "variable_routes.py should be removed")
            else:
                self.log_result("Variable Routes Cleanup", True, "variable_routes.py properly removed")
    
    def check_frontend_integration(self):
        """Check frontend integration"""
        print("\n" + "="*60)
        print("🖥️ CHECKING FRONTEND INTEGRATION")
        print("="*60)
        
        # Check if variable service exists
        variable_service = "workflow-builder-app/src/services/variableService.ts"
        self.check_file_exists(variable_service, "Frontend Variable Service")
        
        # Check if enhanced components exist
        credential_manager = "workflow-builder-app/src/components/credentials/CredentialManager.tsx"
        self.check_file_exists(credential_manager, "Enhanced Credential Manager")
        
        global_var_page = "workflow-builder-app/src/app/(features)/settings/global-variable/page.tsx"
        self.check_file_exists(global_var_page, "Enhanced Global Variable Page")
    
    def run_integration_tests(self):
        """Run integration tests if services are available"""
        print("\n" + "="*60)
        print("🔗 RUNNING INTEGRATION TESTS")
        print("="*60)
        
        # Check if services are running (optional)
        print("Note: Integration tests require running services")
        print("Skipping live integration tests - run manually if services are available")
        
        # Check if integration test script exists
        integration_test = "test_enhanced_credentials_endpoint.py"
        self.check_file_exists(integration_test, "Integration Test Script")
        
        if os.path.exists(integration_test):
            print("✅ Integration test script available for manual execution")
            print("Run: python test_enhanced_credentials_endpoint.py")
        else:
            self.log_result("Integration Test Script", False, "Script not found")
    
    def generate_report(self):
        """Generate test report"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t["success"]])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("📊 TEST REPORT")
        print("="*60)
        print(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.test_results:
                if not test["success"]:
                    print(f"  - {test['test']}: {test['message']}")
                    if test.get("details"):
                        print(f"    Details: {test['details']}")
        
        # Save detailed report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "summary": {
                    "start_time": self.start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100
                },
                "test_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return failed_tests == 0
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Comprehensive Test Suite for Unified Variables System")
        print("="*80)
        
        # Run all test categories
        self.check_proto_compilation()
        self.check_database_migration()
        self.check_api_gateway_routes()
        self.check_frontend_integration()
        self.run_unit_tests()
        self.run_integration_tests()
        
        # Generate final report
        success = self.generate_report()
        
        if success:
            print("\n🎉 All tests passed! The unified variables system is ready for deployment.")
            print("\n✅ Key validations completed:")
            print("  - Proto definitions are valid")
            print("  - Database migration script is ready")
            print("  - API Gateway routes are enhanced")
            print("  - Frontend integration is complete")
            print("  - Unit tests pass")
            print("\n🚀 Next steps:")
            print("  1. Run database migration")
            print("  2. Deploy enhanced API Gateway")
            print("  3. Deploy enhanced frontend")
            print("  4. Run integration tests with live services")
        else:
            print("\n💥 Some tests failed. Please review the issues above.")
            print("Fix the failing tests before proceeding with deployment.")
        
        return success

def main():
    """Main test runner"""
    runner = TestRunner()
    success = runner.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
