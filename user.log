  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/redis/client.py", line 559, in execute_command
    return self._execute_command(*args, **options)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/redis/client.py", line 565, in _execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/redis/connection.py", line 1422, in get_connection
    connection.connect()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/redis/connection.py", line 363, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 61 connecting to 127.0.0.1:6379. Connection refused.
Pratham-ka-MacBook-Air:user-service prathamagarwal$ clear
Pratham-ka-MacBook-Air:user-service prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Repository already exists at: /Users/<USER>/Desktop/ruh_ai/backend/user-service/proto-definitions, removing and cloning fresh copy
Cloning into 'proto-definitions'...
remote: Enumerating objects: 943, done.
remote: Counting objects: 100% (99/99), done.
remote: Compressing objects: 100% (99/99), done.
remote: Total 943 (delta 38), reused 2 (delta 0), pack-reused 844
Receiving objects: 100% (943/943), 236.56 KiB | 1.50 MiB/s, done.
Resolving deltas: 100% (500/500), done.
Successfully cloned fresh copy of repository: https://oauth2:<EMAIL>/ruh.ai/proto-definitions.git user-variables
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting User Service...
2025-07-25 12:26:17 [info     ] User service started on port 50052
