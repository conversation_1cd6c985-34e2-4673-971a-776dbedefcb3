    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 156, in wait
    _wait_once(wait_fn, MAXIMUM_WAIT_TIMEOUT, spin_cb)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/user-service-I00z0Y2q-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 116, in _wait_once
    wait_fn(timeout=timeout)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 659, in wait
    signaled = self._cond.wait(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
^C
Pratham-ka-MacBook-Air:user-service prathamagarwal$ 
Pratham-ka-MacBook-Air:user-service prathamagarwal$ clear
Pratham-ka-MacBook-Air:user-service prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Repository already exists at: /Users/<USER>/Desktop/ruh_ai/backend/user-service/proto-definitions, removing and cloning fresh copy
Cloning into 'proto-definitions'...
remote: Enumerating objects: 943, done.
remote: Counting objects: 100% (99/99), done.
remote: Compressing objects: 100% (99/99), done.
remote: Total 943 (delta 38), reused 2 (delta 0), pack-reused 844
Receiving objects: 100% (943/943), 236.56 KiB | 9.46 MiB/s, done.
Resolving deltas: 100% (500/500), done.
Successfully cloned fresh copy of repository: https://oauth2:<EMAIL>/ruh.ai/proto-definitions.git user-variables
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting User Service...
2025-07-25 14:03:34 [info     ] User service started on port 50052
